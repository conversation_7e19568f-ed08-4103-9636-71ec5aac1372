import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../utils/constants.dart' as constants;
import '../utils/currency_formatter.dart';
import '../services/transaction_service.dart';
import '../models/transaction_model.dart';
import '../services/gyro_tracker.dart';

class ActivityScreen extends StatefulWidget {
  const ActivityScreen({Key? key}) : super(key: key);

  @override
  State<ActivityScreen> createState() => _ActivityScreenState();
}

class _ActivityScreenState extends State<ActivityScreen> {
  String selectedPeriod = 'Week';
  final TransactionService _transactionService = TransactionService();
  List<Transaction> _transactions = [];
  TransactionSummary? _summary;
  bool _isLoading = true;
  Offset? _swipeStart;
  DateTime? _swipeStartTime;
  List<double> _swipeVelocities = [];
  List<Map<String, double>> _tapPositions = [];


  @override
  void initState() {
    super.initState();
    _loadActivityData();
     GyroTracker().startPassive();
  }

  void _handleTapDown(TapDownDetails details) {
  final position = details.globalPosition;
  _tapPositions.add({
    'x': position.dx,
    'y': position.dy,
  });
}

  
  void _handleSwipeStart(DragStartDetails details) {
  _swipeStart = details.localPosition;
  _swipeStartTime = DateTime.now();
}

void _handleSwipeEnd(DragEndDetails details) {
  if (_swipeStart == null || _swipeStartTime == null) return;

  final endTime = DateTime.now();
  final durationMs = endTime.difference(_swipeStartTime!).inMilliseconds;
  if (durationMs > 0) {
    final velocity = details.velocity.pixelsPerSecond.distance;
    final seconds = durationMs / 1000;
    final avgVelocity = velocity / seconds;
    _swipeVelocities.add(avgVelocity);
  }

  _swipeStart = null;
  _swipeStartTime = null;
}


  Future<void> _loadActivityData() async {
    try {
      await _transactionService.initialize();
      _updateDataForPeriod();
    } catch (e) {
      print('Error loading activity data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _updateDataForPeriod() {
    final now = DateTime.now();
    DateTime startDate;

    switch (selectedPeriod) {
      case 'Week':
        startDate = now.subtract(const Duration(days: 7));
        break;
      case 'Month':
        startDate = DateTime(now.year, now.month - 1, now.day);
        break;
      case 'Year':
        startDate = DateTime(now.year - 1, now.month, now.day);
        break;
      default:
        startDate = now.subtract(const Duration(days: 7));
    }

    setState(() {
      _transactions = _transactionService.getTransactionsForPeriod(startDate, now);
      _summary = _transactionService.getTransactionSummary(startDate, now);
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
          onPanStart: _handleSwipeStart,
          onPanEnd: _handleSwipeEnd,
              behavior: HitTestBehavior.translucent,
             onTapDown: _handleTapDown,
      child: Scaffold(
        backgroundColor: constants.AppColors.bankingBackground,
        appBar: AppBar(
          backgroundColor: constants.AppColors.bankingBackground,
          elevation: 0,
          leading: IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          ),
          title: const Text(
            'Activity',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          centerTitle: true,
          actions: [
            IconButton(
              onPressed: () {},
              icon: const Icon(Icons.notifications_outlined, color: Colors.white),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Period Selection
              _buildPeriodSelection(),
              const SizedBox(height: 30),
              
              // Chart
              _buildChart(),
              const SizedBox(height: 30),
              
              // Stats Cards
              _buildStatsCards(),
              const SizedBox(height: 30),
              
              // Saving Pocket
              _buildSavingPocket(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodSelection() {
    final periods = ['Week', 'Month', 'Year'];
    
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: periods.map((period) {
          final isSelected = period == selectedPeriod;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  selectedPeriod = period;
                  _isLoading = true;
                });
                _updateDataForPeriod();
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? constants.AppColors.bankingPrimary : Colors.transparent,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  period,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildChart() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildLegendItem('Earned', constants.AppColors.creditGreen),
              const SizedBox(width: 20),
              _buildLegendItem('Spent', constants.AppColors.debitRed),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: 80,
                barTouchData: BarTouchData(enabled: false),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        const months = ['Jan', 'Feb', 'Apr', 'Mar', 'May', 'Jun'];
                        if (value.toInt() >= 0 && value.toInt() < months.length) {
                          return Text(
                            months[value.toInt()],
                            style: const TextStyle(color: Colors.grey, fontSize: 12),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          '₹${(value.toInt() * 1000).toDouble().inrNoSymbol}',
                          style: const TextStyle(color: Colors.grey, fontSize: 12),
                        );
                      },
                    ),
                  ),
                ),
                borderData: FlBorderData(show: false),
                gridData: const FlGridData(show: false),
                barGroups: [
                  _buildBarGroup(0, 30, 25),
                  _buildBarGroup(1, 40, 35),
                  _buildBarGroup(2, 70, 50),
                  _buildBarGroup(3, 45, 40),
                  _buildBarGroup(4, 60, 45),
                  _buildBarGroup(5, 75, 55),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  BarChartGroupData _buildBarGroup(int x, double earned, double spent) {
    return BarChartGroupData(
      x: x,
      barRods: [
        BarChartRodData(
          toY: earned,
          color: constants.AppColors.creditGreen,
          width: 12,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
          ),
        ),
        BarChartRodData(
          toY: spent,
          color: constants.AppColors.debitRed,
          width: 12,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildStatsCards() {
    if (_isLoading || _summary == null) {
      return const Row(
        children: [
          Expanded(
            child: Center(child: CircularProgressIndicator(color: Colors.white)),
          ),
        ],
      );
    }

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Earned',
            _summary!.formattedIncome,
            constants.AppColors.creditGreen,
            Icons.trending_up,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'Spent',
            _summary!.formattedExpense,
            constants.AppColors.debitRed,
            Icons.trending_down,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String amount, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            amount,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavingPocket() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Saving Pocket',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {},
              child: Text(
                'See all',
                style: TextStyle(
                  color: constants.AppColors.bankingPrimary,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildSavingItem(
          'Emergency Fund',
          '75% of ${50000.0.inr}',
          'High Priority Savings',
          37500.0.inr,
          'This Month',
          Icons.security,
        ),
        const SizedBox(height: 12),
        _buildSavingItem(
          'MacBook Pro 2024',
          '45% of ${180000.0.inr}',
          'Apple Store India',
          81000.0.inr,
          'Next Month',
          Icons.laptop_mac,
        ),
      ],
    );
  }

  Widget _buildSavingItem(
    String title,
    String progress,
    String vendor,
    String amount,
    String date,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: constants.AppColors.bankingBackground,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  progress,
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  vendor,
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                amount,
                style: TextStyle(
                  color: constants.AppColors.creditGreen,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                date,
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  @override
void dispose() {
  GyroTracker().stopPassive(); // ✅ Stop timer & subscription
  super.dispose();
}

}
