import 'package:flutter/material.dart';
import '../utils/constants.dart' as constants;

class HelpCenterScreen extends StatelessWidget {
  const HelpCenterScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: constants.AppColors.bankingBackground,
      appBar: AppBar(
        title: const Text(
          'Help Center',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: constants.AppColors.bankingBackground,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildHelpItem(
              icon: Icons.question_answer,
              title: 'Frequently Asked Questions',
              subtitle: 'Find answers to common questions',
              onTap: () {},
            ),
            const SizedBox(height: 16),
            _buildHelpItem(
              icon: Icons.security,
              title: 'Security & Privacy',
              subtitle: 'Learn about our security measures',
              onTap: () {},
            ),
            const SizedBox(height: 16),
            _buildHelpItem(
              icon: Icons.account_balance,
              title: 'Banking Services',
              subtitle: 'Information about our services',
              onTap: () {},
            ),
            const SizedBox(height: 16),
            _buildHelpItem(
              icon: Icons.phone,
              title: 'Contact Support',
              subtitle: 'Get in touch with our support team',
              onTap: () {
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1B263B),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: constants.AppColors.bankingPrimary,
          size: 28,
        ),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 14,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: Colors.white54,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }
}
