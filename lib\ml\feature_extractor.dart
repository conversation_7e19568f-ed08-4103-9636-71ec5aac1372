import 'dart:math' as math;
import 'behavioral_data.dart';

/// Advanced feature extraction for behavioral biometric authentication
/// Extracts temporal, statistical, and pattern-based features from behavioral data
class FeatureExtractor {
  // Feature extraction parameters
  static const int _windowSize = 5; // Sliding window for temporal features
  static const double _velocityThreshold = 50.0; // Minimum velocity for gesture detection
  
  /// Extract comprehensive feature vector from behavioral data
  List<double> extractFeatures(List<BehavioralData> behavioralSequence) {
    if (behavioralSequence.isEmpty) {
      return List.filled(getFeatureCount(), 0.0);
    }
    
    final features = <double>[];
    
    // 1. Basic statistical features (5 features)
    features.addAll(_extractBasicFeatures(behavioralSequence.last));
    
    // 2. Temporal pattern features (8 features)
    features.addAll(_extractTemporalFeatures(behavioralSequence));
    
    // 3. Velocity and acceleration features (6 features)
    features.addAll(_extractMotionFeatures(behavioralSequence));
    
    // 4. Pressure dynamics features (4 features)
    features.addAll(_extractPressureFeatures(behavioralSequence));
    
    // 5. Rhythm and timing features (5 features)
    features.addAll(_extractRhythmFeatures(behavioralSequence));
    
    // 6. Device orientation features (3 features)
    features.addAll(_extractOrientationFeatures(behavioralSequence));
    
    // 7. Consistency and stability features (4 features)
    features.addAll(_extractConsistencyFeatures(behavioralSequence));
    
    return features;
  }
  
  /// Extract basic statistical features from single behavioral sample
  List<double> _extractBasicFeatures(BehavioralData data) {
    return [
      data.touchPressure,
      data.touchDuration,
      data.swipeVelocity,
      data.deviceTilt,
      data.typingRhythm,
    ];
  }
  
  /// Extract temporal pattern features from sequence
  List<double> _extractTemporalFeatures(List<BehavioralData> sequence) {
    if (sequence.length < 2) {
      return List.filled(8, 0.0);
    }
    
    final features = <double>[];
    
    // Time intervals between actions
    final intervals = <double>[];
    for (int i = 1; i < sequence.length; i++) {
      final interval = sequence[i].timestamp.difference(sequence[i-1].timestamp).inMilliseconds.toDouble();
      intervals.add(interval);
    }
    
    if (intervals.isNotEmpty) {
      features.add(_calculateMean(intervals)); // Mean interval
      features.add(_calculateStandardDeviation(intervals)); // Interval variability
      features.add(intervals.reduce(math.min)); // Min interval
      features.add(intervals.reduce(math.max)); // Max interval
    } else {
      features.addAll([0.0, 0.0, 0.0, 0.0]);
    }
    
    // Sequence length and density
    features.add(sequence.length.toDouble());
    
    // Time span of sequence
    final timeSpan = sequence.last.timestamp.difference(sequence.first.timestamp).inMilliseconds.toDouble();
    features.add(timeSpan);
    
    // Action density (actions per second)
    features.add(timeSpan > 0 ? (sequence.length / (timeSpan / 1000.0)) : 0.0);
    
    // Temporal regularity (coefficient of variation)
    features.add(intervals.isNotEmpty && _calculateMean(intervals) > 0 
        ? _calculateStandardDeviation(intervals) / _calculateMean(intervals) 
        : 0.0);
    
    return features;
  }
  
  /// Extract motion-based features (velocity, acceleration)
  List<double> _extractMotionFeatures(List<BehavioralData> sequence) {
    if (sequence.length < 2) {
      return List.filled(6, 0.0);
    }
    
    final velocities = sequence.map((d) => d.swipeVelocity).toList();
    final accelerations = <double>[];
    
    // Calculate accelerations
    for (int i = 1; i < velocities.length; i++) {
      accelerations.add(velocities[i] - velocities[i-1]);
    }
    
    return [
      _calculateMean(velocities), // Mean velocity
      _calculateStandardDeviation(velocities), // Velocity variability
      velocities.reduce(math.max), // Peak velocity
      accelerations.isNotEmpty ? _calculateMean(accelerations) : 0.0, // Mean acceleration
      accelerations.isNotEmpty ? _calculateStandardDeviation(accelerations) : 0.0, // Acceleration variability
      accelerations.isNotEmpty ? accelerations.reduce(math.max) : 0.0, // Peak acceleration
    ];
  }
  
  /// Extract pressure dynamics features
  List<double> _extractPressureFeatures(List<BehavioralData> sequence) {
    final pressures = sequence.map((d) => d.touchPressure).toList();
    
    return [
      _calculateMean(pressures), // Mean pressure
      _calculateStandardDeviation(pressures), // Pressure variability
      pressures.reduce(math.min), // Min pressure
      pressures.reduce(math.max), // Max pressure
    ];
  }
  
  /// Extract rhythm and timing features
  List<double> _extractRhythmFeatures(List<BehavioralData> sequence) {
    final rhythms = sequence.map((d) => d.typingRhythm).toList();
    final durations = sequence.map((d) => d.touchDuration).toList();
    
    return [
      _calculateMean(rhythms), // Mean typing rhythm
      _calculateStandardDeviation(rhythms), // Rhythm variability
      _calculateMean(durations), // Mean touch duration
      _calculateStandardDeviation(durations), // Duration variability
      durations.isNotEmpty && _calculateMean(durations) > 0 
          ? _calculateStandardDeviation(durations) / _calculateMean(durations) 
          : 0.0, // Duration consistency
    ];
  }
  
  /// Extract device orientation features
  List<double> _extractOrientationFeatures(List<BehavioralData> sequence) {
    final tilts = sequence.map((d) => d.deviceTilt).toList();
    
    return [
      _calculateMean(tilts), // Mean tilt
      _calculateStandardDeviation(tilts), // Tilt variability
      tilts.reduce(math.max) - tilts.reduce(math.min), // Tilt range
    ];
  }
  
  /// Extract consistency and stability features
  List<double> _extractConsistencyFeatures(List<BehavioralData> sequence) {
    if (sequence.length < 3) {
      return List.filled(4, 0.0);
    }
    
    // Calculate feature stability across sequence
    final pressureStability = _calculateStability(sequence.map((d) => d.touchPressure).toList());
    final velocityStability = _calculateStability(sequence.map((d) => d.swipeVelocity).toList());
    final rhythmStability = _calculateStability(sequence.map((d) => d.typingRhythm).toList());
    
    // Overall behavioral consistency score
    final consistencyScore = (pressureStability + velocityStability + rhythmStability) / 3.0;
    
    return [
      pressureStability,
      velocityStability,
      rhythmStability,
      consistencyScore,
    ];
  }
  
  /// Calculate stability metric for a feature sequence
  double _calculateStability(List<double> values) {
    if (values.length < 2) return 1.0;
    
    final mean = _calculateMean(values);
    final std = _calculateStandardDeviation(values);
    
    // Stability = 1 / (1 + coefficient_of_variation)
    return mean > 0 ? 1.0 / (1.0 + (std / mean)) : 1.0;
  }
  
  /// Calculate mean of a list
  double _calculateMean(List<double> values) {
    if (values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }
  
  /// Calculate standard deviation of a list
  double _calculateStandardDeviation(List<double> values) {
    if (values.length < 2) return 0.0;
    
    final mean = _calculateMean(values);
    final variance = values.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) / values.length;
    return math.sqrt(variance);
  }
  
  /// Get total number of features extracted
  int getFeatureCount() {
    return 35; // 5 + 8 + 6 + 4 + 5 + 3 + 4 = 35 features
  }
  
  /// Get feature names for interpretability
  List<String> getFeatureNames() {
    return [
      // Basic features (5)
      'touch_pressure', 'touch_duration', 'swipe_velocity', 'device_tilt', 'typing_rhythm',
      
      // Temporal features (8)
      'mean_interval', 'interval_variability', 'min_interval', 'max_interval',
      'sequence_length', 'time_span', 'action_density', 'temporal_regularity',
      
      // Motion features (6)
      'mean_velocity', 'velocity_variability', 'peak_velocity',
      'mean_acceleration', 'acceleration_variability', 'peak_acceleration',
      
      // Pressure features (4)
      'mean_pressure', 'pressure_variability', 'min_pressure', 'max_pressure',
      
      // Rhythm features (5)
      'mean_rhythm', 'rhythm_variability', 'mean_duration', 'duration_variability', 'duration_consistency',
      
      // Orientation features (3)
      'mean_tilt', 'tilt_variability', 'tilt_range',
      
      // Consistency features (4)
      'pressure_stability', 'velocity_stability', 'rhythm_stability', 'overall_consistency',
    ];
  }
  
  /// Normalize features using z-score normalization
  List<double> normalizeFeatures(List<double> features, List<double> means, List<double> stds) {
    final normalized = <double>[];
    
    for (int i = 0; i < features.length; i++) {
      if (i < means.length && i < stds.length && stds[i] > 0) {
        normalized.add((features[i] - means[i]) / stds[i]);
      } else {
        normalized.add(features[i]);
      }
    }
    
    return normalized;
  }
  
  /// Calculate feature importance based on variance
  Map<String, double> calculateFeatureImportance(List<List<double>> featureMatrix) {
    final importance = <String, double>{};
    final featureNames = getFeatureNames();
    
    if (featureMatrix.isEmpty) return importance;
    
    final featureCount = featureMatrix.first.length;
    
    for (int i = 0; i < featureCount && i < featureNames.length; i++) {
      final featureValues = featureMatrix.map((row) => i < row.length ? row[i] : 0.0).toList();
      final variance = _calculateStandardDeviation(featureValues);
      importance[featureNames[i]] = variance;
    }
    
    return importance;
  }
}
