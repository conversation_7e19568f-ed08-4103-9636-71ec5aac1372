import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorageService {
  final _storage = const FlutterSecureStorage();

  Future<void> write(String key, String value) async {
    await _storage.write(key: key, value: value);
  }
  
  Future<Map<String, String>?> getCredentials() async {
  final email = await read('user_email');
  final password = await read('user_password');
  if (email != null && password != null) {
    return {'email': email, 'password': password};
  }
  return null;
}


  Future<String?> read(String key) async {
    return await _storage.read(key: key);
  }

  Future<void> delete(String key) async {
    await _storage.delete(key: key);
  }

  Future<void> saveCredentials(String email, String password) async {
    await write('user_email', email);
    await write('user_password', password);
  }

  Future<void> clearCredentials() async {
    await delete('user_email');
    await delete('user_password');
  }
}
