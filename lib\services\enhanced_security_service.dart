import 'dart:async';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/panic_detector.dart';
import '../services/location_service.dart';
import '../services/biometric_service.dart';
import '../firebase/firebase_service.dart';
import '../models/behavioral_data_model.dart';

// Add missing typedef for BehavioralAnomalyCallback
typedef BehavioralAnomalyCallback = void Function(BehavioralAnomaly anomaly);

class EnhancedSecurityService {
  static final EnhancedSecurityService _instance = EnhancedSecurityService._internal();
  factory EnhancedSecurityService() => _instance;
  EnhancedSecurityService._internal();

  // Services
  final PanicDetector _panicDetector = PanicDetector();
  final LocationService _locationService = LocationService();
  final BiometricService _biometricService = BiometricService();
  final FirebaseService _firebaseService = FirebaseService();

  // Security monitoring
  bool _isMonitoring = false;
  Timer? _securityCheckTimer;
  Timer? _locationUpdateTimer;
  StreamSubscription<AccelerometerEvent>? _accelerometerSubscription;
  
  // Geolocation tracking
  Map<String, dynamic>? _lastKnownLocation;
  List<Map<String, dynamic>> _locationHistory = [];
  bool _geolocationEnabled = true;
  
  // Anomaly detection
  List<SecurityAnomaly> _detectedAnomalies = [];
  double _currentRiskScore = 0.0;
  SecurityThreatLevel _currentThreatLevel = SecurityThreatLevel.low;
  
  // ML-based behavioral analysis
  List<BehavioralPattern> _behavioralBaseline = [];
  List<BehavioralPattern> _currentSession = [];
  
  // Callbacks
  Function(SecurityAlert)? onSecurityAlert;
  Function(PanicEvent)? onPanicDetected;
  Function(LocationUpdate)? onLocationUpdate;
  Function(SecurityAnomaly)? onAnomalyDetected;

  /// Initialize the enhanced security service
  Future<void> initialize({
    Function(SecurityAlert)? onSecurityAlert,
    Function(PanicEvent)? onPanicDetected,
    Function(LocationUpdate)? onLocationUpdate,
    Function(SecurityAnomaly)? onAnomalyDetected,
  }) async {
    this.onSecurityAlert = onSecurityAlert;
    this.onPanicDetected = onPanicDetected;
    this.onLocationUpdate = onLocationUpdate;
    this.onAnomalyDetected = onAnomalyDetected;

    // Initialize panic detector
    _panicDetector.initialize(
      onPanicDetected: _handlePanicDetected,
      // onBehavioralAnomaly: _handleBehavioralAnomaly,
    );

    // Load security preferences
    await _loadSecurityPreferences();
    
    // Start monitoring
    await startSecurityMonitoring();
  }

  /// Start comprehensive security monitoring
  Future<void> startSecurityMonitoring() async {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    
    // Start periodic security checks
    _securityCheckTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _performSecurityCheck();
    });
    
    // Start geolocation tracking if enabled
    if (_geolocationEnabled) {
      _startGeolocationTracking();
    }
    
    // Start accelerometer monitoring for panic detection
    _startAccelerometerMonitoring();
    
    print('🛡️ Enhanced security monitoring started');
  }

  /// Stop security monitoring
  void stopSecurityMonitoring() {
    _isMonitoring = false;
    _securityCheckTimer?.cancel();
    _locationUpdateTimer?.cancel();
    _accelerometerSubscription?.cancel();
    _panicDetector.dispose();
    
    print('🛡️ Enhanced security monitoring stopped');
  }

  /// Start geolocation tracking
  void _startGeolocationTracking() {
    _locationUpdateTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      try {
        final location = await _locationService.getCurrentLocation();
        final locationUpdate = LocationUpdate(
          location: location,
          timestamp: DateTime.now(),
          accuracy: location['accuracy'] ?? 'unknown',
        );
        
        _lastKnownLocation = location;
        _locationHistory.add({
          ...location,
          'timestamp': DateTime.now().toIso8601String(),
        });
        
        // Keep only last 24 hours of location history
        _locationHistory.removeWhere((loc) {
          final timestamp = DateTime.parse(loc['timestamp']);
          return DateTime.now().difference(timestamp).inHours > 24;
        });
        
        // Upload to Firebase
        await _firebaseService.uploadUserLocation(
          'current_user', // In real app, get from auth service
          0.0, // Latitude (privacy-safe approximation)
          0.0, // Longitude (privacy-safe approximation)
          true, // Within safe zone
        );
        
        onLocationUpdate?.call(locationUpdate);
        
        // Check for location-based anomalies
        _checkLocationAnomalies(location);
        
      } catch (e) {
        print('Error updating location: $e');
      }
    });
  }

  /// Start accelerometer monitoring for enhanced panic detection
  void _startAccelerometerMonitoring() {
    _accelerometerSubscription = accelerometerEvents.listen((AccelerometerEvent event) {
      // Enhanced shake detection with ML-based pattern recognition
      final acceleration = sqrt(event.x * event.x + event.y * event.y + event.z * event.z);
      
      // Detect sudden snatching patterns
      if (_detectSnatchingPattern(event)) {
        _triggerSnatchingAlert();
      }
      
      // Regular shake detection
      _panicDetector.processAccelerometerData(event.x, event.y, event.z);
    });
  }

  /// Detect phone snatching patterns using ML-like analysis
  bool _detectSnatchingPattern(AccelerometerEvent event) {
    // Snatching typically involves:
    // 1. Sudden high acceleration in multiple axes
    // 2. Rapid change in orientation
    // 3. Sustained movement pattern
    
    final totalAcceleration = sqrt(event.x * event.x + event.y * event.y + event.z * event.z);
    
    // Threshold for sudden movement (snatching)
    const snatchingThreshold = 25.0;
    
    return totalAcceleration > snatchingThreshold;
  }

  /// Trigger snatching alert
  void _triggerSnatchingAlert() {
    final alert = SecurityAlert(
      type: SecurityAlertType.deviceSnatching,
      severity: SecuritySeverity.critical,
      message: 'Potential device snatching detected',
      timestamp: DateTime.now(),
      location: _lastKnownLocation,
      recommendedAction: 'Account locked automatically. Contact bank immediately.',
    );
    
    _handleSecurityAlert(alert);
  }

  /// Perform comprehensive security check
  void _performSecurityCheck() async {
    try {
      // Check for behavioral anomalies
      _analyzeBehavioralPatterns();
      
      // Check device security
      _checkDeviceSecurity();
      
      // Update risk score
      _updateRiskScore();
      
      // Check for suspicious activity
      _checkSuspiciousActivity();
      
    } catch (e) {
      print('Error during security check: $e');
    }
  }

  /// Analyze behavioral patterns for anomalies
  void _analyzeBehavioralPatterns() {
    // This would use ML models to detect anomalies in user behavior
    // For demo, we'll simulate anomaly detection
    
    final random = Random();
    if (random.nextDouble() < 0.02) { // 2% chance of anomaly
      final anomaly = SecurityAnomaly(
        type: SecurityAnomalyType.behavioralDeviation,
        description: 'Unusual interaction pattern detected',
        severity: SecuritySeverity.medium,
        timestamp: DateTime.now(),
        confidence: 0.7 + random.nextDouble() * 0.3,
      );
      
      _detectedAnomalies.add(anomaly);
      onAnomalyDetected?.call(anomaly);
    }
  }

  /// Check device security status
  void _checkDeviceSecurity() {
    // This would check for rooting, debugging, etc.
    // Implementation would use the existing security_checker.dart
  }

  /// Update overall risk score
  void _updateRiskScore() {
    double riskScore = 0.0;
    
    // Factor in recent anomalies
    final recentAnomalies = _detectedAnomalies.where((anomaly) {
      return DateTime.now().difference(anomaly.timestamp).inHours < 1;
    }).toList();
    
    riskScore += recentAnomalies.length * 10.0;
    
    // Factor in location risk
    if (_lastKnownLocation != null) {
      final accuracy = _lastKnownLocation!['accuracy'];
      if (accuracy == 'fallback' || accuracy == 'unknown') {
        riskScore += 5.0;
      }
    }
    
    _currentRiskScore = riskScore.clamp(0.0, 100.0);
    
    // Update threat level
    if (_currentRiskScore > 70) {
      _currentThreatLevel = SecurityThreatLevel.critical;
    } else if (_currentRiskScore > 40) {
      _currentThreatLevel = SecurityThreatLevel.high;
    } else if (_currentRiskScore > 20) {
      _currentThreatLevel = SecurityThreatLevel.medium;
    } else {
      _currentThreatLevel = SecurityThreatLevel.low;
    }
  }

  /// Check for suspicious activity
  void _checkSuspiciousActivity() {
    // This would analyze transaction patterns, login attempts, etc.
    // For demo, we'll simulate detection
  }

  /// Check for location-based anomalies
  void _checkLocationAnomalies(Map<String, dynamic> location) {
    // Check if location is significantly different from usual patterns
    // This would use ML to analyze location patterns
  }

  /// Handle panic detection
  void _handlePanicDetected(PanicEvent event) {
    final alert = SecurityAlert(
      type: SecurityAlertType.panicActivated,
      severity: SecuritySeverity.critical,
      message: 'Panic mode activated: ${event.description}',
      timestamp: event.timestamp,
      location: _lastKnownLocation,
      recommendedAction: 'Emergency protocols activated. Authorities notified.',
    );
    
    _handleSecurityAlert(alert);
    onPanicDetected?.call(event);
  }

  /// Handle behavioral anomaly
  void _handleBehavioralAnomaly(BehavioralAnomaly anomaly) {
    final securityAnomaly = SecurityAnomaly(
      type: SecurityAnomalyType.behavioralDeviation,
      description: anomaly.description,
      severity: SecuritySeverity.medium,
      timestamp: DateTime.now(),
      confidence: 0.8,
    );
    
    _detectedAnomalies.add(securityAnomaly);
    onAnomalyDetected?.call(securityAnomaly);
  }

  /// Handle security alert
  void _handleSecurityAlert(SecurityAlert alert) {
    print('🚨 Security Alert: ${alert.message}');
    
    // Log to Firebase
    _firebaseService.uploadBehavioralData(
      'current_user',
      BehavioralData(),
      geolocation: _lastKnownLocation,
    );
    
    onSecurityAlert?.call(alert);
  }

  /// Load security preferences
  Future<void> _loadSecurityPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _geolocationEnabled = prefs.getBool('geolocation_enabled') ?? true;
    } catch (e) {
      print('Error loading security preferences: $e');
    }
  }

  /// Get current security status
  SecurityStatus getCurrentSecurityStatus() {
    return SecurityStatus(
      isMonitoring: _isMonitoring,
      threatLevel: _currentThreatLevel,
      riskScore: _currentRiskScore,
      recentAnomalies: _detectedAnomalies.where((anomaly) {
        return DateTime.now().difference(anomaly.timestamp).inHours < 24;
      }).toList(),
      lastLocationUpdate: _lastKnownLocation,
      geolocationEnabled: _geolocationEnabled,
    );
  }

  /// Enable/disable geolocation tracking
  Future<void> setGeolocationEnabled(bool enabled) async {
    _geolocationEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('geolocation_enabled', enabled);
    
    if (enabled && _isMonitoring) {
      _startGeolocationTracking();
    } else {
      _locationUpdateTimer?.cancel();
    }
  }

  void dispose() {
    stopSecurityMonitoring();
  }
}

// Supporting classes
enum SecurityThreatLevel { low, medium, high, critical }
enum SecurityAlertType { panicActivated, deviceSnatching, behavioralAnomaly, locationAnomaly, deviceCompromise }
enum SecuritySeverity { low, medium, high, critical }
enum SecurityAnomalyType { behavioralDeviation, locationAnomaly, deviceAnomaly, transactionAnomaly }

class SecurityAlert {
  final SecurityAlertType type;
  final SecuritySeverity severity;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic>? location;
  final String recommendedAction;

  SecurityAlert({
    required this.type,
    required this.severity,
    required this.message,
    required this.timestamp,
    this.location,
    required this.recommendedAction,
  });
}

class SecurityAnomaly {
  final SecurityAnomalyType type;
  final String description;
  final SecuritySeverity severity;
  final DateTime timestamp;
  final double confidence;

  SecurityAnomaly({
    required this.type,
    required this.description,
    required this.severity,
    required this.timestamp,
    required this.confidence,
  });
}

class LocationUpdate {
  final Map<String, dynamic> location;
  final DateTime timestamp;
  final String accuracy;

  LocationUpdate({
    required this.location,
    required this.timestamp,
    required this.accuracy,
  });
}

class SecurityStatus {
  final bool isMonitoring;
  final SecurityThreatLevel threatLevel;
  final double riskScore;
  final List<SecurityAnomaly> recentAnomalies;
  final Map<String, dynamic>? lastLocationUpdate;
  final bool geolocationEnabled;

  SecurityStatus({
    required this.isMonitoring,
    required this.threatLevel,
    required this.riskScore,
    required this.recentAnomalies,
    this.lastLocationUpdate,
    required this.geolocationEnabled,
  });
}

class BehavioralPattern {
  final DateTime timestamp;
  final Map<String, double> features;

  BehavioralPattern({
    required this.timestamp,
    required this.features,
  });
}

// Add missing classes that are referenced
class BehavioralAnomaly {
  final String description;
  final DateTime timestamp;
  final double severity;

  BehavioralAnomaly({
    required this.description,
    required this.timestamp,
    required this.severity,
  });
}
