import 'package:sensors_plus/sensors_plus.dart';

class PanicEvent {
  final String description;
  final DateTime timestamp;

  PanicEvent({
    required this.description,
    required this.timestamp,
  });
}

typedef PanicDetectedCallback = void Function(PanicEvent event);
typedef BehavioralAnomalyCallback = void Function(dynamic anomaly);

class PanicDetector {
  PanicDetectedCallback? onPanicDetected;
  BehavioralAnomalyCallback? onBehavioralAnomaly;

  void initialize({
    PanicDetectedCallback? onPanicDetected,
    BehavioralAnomalyCallback? onBehavioralAnomaly,
  }) {
    this.onPanicDetected = onPanicDetected;
    this.onBehavioralAnomaly = onBehavioralAnomaly;
  }

  void processAccelerometerData(double x, double y, double z) {
    // Dummy shake detection logic
    final magnitude = (x * x + y * y + z * z).abs();
    if (magnitude > 30) {
      onPanicDetected?.call(PanicEvent(
        description: 'Shake detected',
        timestamp: DateTime.now(),
      ));
    }
  }

  void dispose() {
    // Clean up resources if needed
  }
}