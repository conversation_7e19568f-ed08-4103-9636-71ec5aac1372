import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../widgets/app_widgets.dart';
import '../services/auth_service.dart';
import '../services/local_auth_service.dart';
import '../services/background_anomaly_service.dart';
import '../services/security_service.dart';
import '../services/biometric_service.dart';
import '../services/ml_safety_predictor.dart';
import '../routes/app_routes.dart';
import '../utils/constants.dart' as constants;
import '../models/behavioral_data_model.dart';
import '../services/location_service.dart';
import 'package:local_auth/local_auth.dart';
import '../services/gyro_tracker.dart';
import '../models/behavioral_data_model.dart';
import '../firebase/firebase_service.dart';
import '../services/hold_angle_tracker.dart';
import '../services/secure_storage_service.dart';
import 'package:geolocator/geolocator.dart';
import '../services/location_permission_service.dart';
import '../services/geo_fence_service.dart';

class SecureLoginScreen extends StatefulWidget {
  const SecureLoginScreen({Key? key}) : super(key: key);

  @override
  State<SecureLoginScreen> createState() => _SecureLoginScreenState();
}

class _SecureLoginScreenState extends State<SecureLoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final HoldAngleTracker _holdAngleTracker = HoldAngleTracker();
  final LocalAuthService _localAuth = LocalAuthService();
  final BackgroundAnomalyService _anomalyService = BackgroundAnomalyService();

  Offset? _swipeStart;
  DateTime? _swipeStartTime;
  List<double> _swipeVelocities = [];
  List<Map<String, double>> _tapPositions = [];
  List<int> _keystrokeIntervals = [];
  DateTime? _lastKeyTime;
  DateTime? _sessionStartTime;

 

  bool _hasUploadedBehavior = false;
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  bool _rememberMe = false;

  // Biometric authentication
  final BiometricService _biometricService = BiometricService();
  bool _biometricAvailable = false;
  List<BiometricType> _availableBiometrics = [];
  String _biometricButtonText = 'Sign in with Biometrics';
  
  // Behavioral tracking

  // DateTime? _typingStartTime;
  List<int> _keystrokeTiming = [];
  // double _averageTypingSpeed = 0.0;
  BehavioralData _currentBehavioralData = BehavioralData();
  
  // Animation controllers
  late AnimationController _shakeController;
  late Animation<double> _shakeAnimation;
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
    // _setupBehavioralTracking();
    _initializeBiometrics();
    enableGyroTracking(); // 🔁 Enable gyro tracking
    GyroTracker().startPassive();
     _holdAngleTracker.start();
     _sessionStartTime = DateTime.now();

  }

  double _calculateAverageSwipeVelocity() {
  if (_swipeVelocities.isEmpty) return 0.0;
  final total = _swipeVelocities.reduce((a, b) => a + b);
  return total / _swipeVelocities.length;
}

  
  void _handleTapDown(TapDownDetails details) {
  final position = details.globalPosition;
  _tapPositions.add({
    'x': position.dx,
    'y': position.dy,
  });
  print('📍 Tap position: x=${position.dx}, y=${position.dy}');
}

  void _handleSwipeStart(DragStartDetails details) {
  _swipeStart = details.localPosition;
  _swipeStartTime = DateTime.now();
}

void _handleSwipeEnd(DragEndDetails details) {
  if (_swipeStart == null || _swipeStartTime == null) return;

  final swipeEndTime = DateTime.now();
  final duration = swipeEndTime.difference(_swipeStartTime!).inMilliseconds;

  if (duration > 0) {
    final velocity = details.velocity.pixelsPerSecond.distance;
    final seconds = duration / 1000;
    final avgVelocity = velocity / seconds;
    _swipeVelocities.add(avgVelocity);

  }

  _swipeStart = null;
  _swipeStartTime = null;
}


  void _setupAnimations() {
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));
  }
  
  // void _setupBehavioralTracking() {
  //   _emailController.addListener(_trackTypingBehavior);
  //   _passwordController.addListener(_trackTypingBehavior);
  // }
  
  // void _trackTypingBehavior() {
  //   final currentTime = DateTime.now();
    
  //   if (_typingStartTime == null) {
  //     _typingStartTime = currentTime;
  //     return;
  //   }
    
  //   final timeDiff = currentTime.difference(_typingStartTime!).inMilliseconds;
  //   _keystrokeTiming.add(timeDiff);
  //   _currentBehavioralData.addKeystroke(currentTime.millisecondsSinceEpoch);
  //   _typingStartTime = currentTime;
    
  //   // Calculate average typing speed
  //   if (_keystrokeTiming.length > 1) {
  //     final totalTime = _keystrokeTiming.reduce((a, b) => a + b);
  //     _averageTypingSpeed = _keystrokeTiming.length / (totalTime / 1000);
  //   }
  // }
  
  Future<void> _handleLogin() async {
  if (!_formKey.currentState!.validate()) {
    _shakeController.forward().then((_) => _shakeController.reverse());
    return;
  }

  setState(() => _isLoading = true);

  try {
    // ✅ Check GPS + permission + geofence radius
    final locationReady = await LocationPermissionService.ensureLocationReady();
    if (!locationReady) {
      _showLocationError("Please enable location & grant permission to continue.");
      return;
    }

    final position = await Geolocator.getCurrentPosition();
final hasSavedLocation = await GeoFenceService.hasSavedLocation();

if (hasSavedLocation) {
  final isInside = await GeoFenceService.isWithinAllowedRadius(position);
  if (!isInside) {
    _showLocationError("Access denied: outside allowed location radius.");
    return;
  }
} else {
  // 🚀 First-time login: Save current location as trusted
  await GeoFenceService.saveCurrentLocation(position);
  print('[GeoFence] No saved location found. Current location saved.');
}


    GyroTracker().startBurst();

    // Upload behavior if not yet done
    if (!_hasUploadedBehavior) {
      await _recordBehavioralData();
      _hasUploadedBehavior = true;
    }

    // Try local authentication first
    final localSuccess = await _localAuth.authenticateWithCredentials(
      _emailController.text.trim(),
      _passwordController.text,
    );
    

    // Also authenticate with old service for compatibility
    final success = await AuthService.login(
      _emailController.text.trim(),
      _passwordController.text,
    );

    if (localSuccess || success) {
  await AuthService.saveCredentials(
    _emailController.text.trim(),
    _passwordController.text,
  );


  // Start background anomaly detection
  await _anomalyService.startBackgroundDetection();

  // Force an immediate anomaly check
  final anomalyScore = await _anomalyService.forceAnomalyCheck();

  // Perform ML safety prediction
  final safetyResult = await _performMLSafetyPrediction();

  // Enhanced safety decision based on both ML and anomaly detection
  await _handleEnhancedSafetyResult(safetyResult, anomalyScore);
}
 else {
      _showError('Invalid email or password');
      _shakeController.forward().then((_) => _shakeController.reverse());
    }
  } catch (e) {
    print('Login error: $e');
    _showError('Login failed. Please try again.');
  } finally {
    setState(() => _isLoading = false);
  }
}
 
 void _showLocationError(String message) {
  _showError(message);
  _shakeController.forward().then((_) => _shakeController.reverse());
}


  
  Future<LoginSafetyResult> _performMLSafetyPrediction() async {
    // Get device information
    final deviceInfo = DeviceInfoPlugin();
    String deviceInfoString = '';
    try {
      final androidInfo = await deviceInfo.androidInfo;
      deviceInfoString = '${androidInfo.brand} ${androidInfo.model}';
    } catch (e) {
      deviceInfoString = 'Unknown Device';
    }
    
    // Get location context
    String locationContext = 'Unknown';
    try {
      final location = await LocationService().getCurrentLocation();
      final region = location['region'] ?? 'Unknown';
      final country = location['country'] ?? 'Unknown';
      locationContext = 'Region_${country}_$region';
    } catch (e) {
      locationContext = 'Location_Unavailable';
    }
    
    // Perform ML prediction
    return await MLSafetyPredictor.predictLoginSafety(
      currentData: _currentBehavioralData,
      userId: _emailController.text.trim(),
      deviceInfo: deviceInfoString,
      loginTime: DateTime.now(),
      locationContext: locationContext,
    );
  }
  
  Future<void> _handleSafetyPredictionResult(LoginSafetyResult result) async {
    print('ML Safety Prediction: ${result.toJson()}');
    
    if (result.isSafe && result.safetyLevel == SafetyLevel.high) {
      // High confidence - direct login
      _showSafetyMessage('Login approved - Safe behavioral patterns detected', Colors.green);
      await Future.delayed(const Duration(milliseconds: 1500));
      Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
      
    } else if (result.isSafe && result.safetyLevel == SafetyLevel.medium) {
      // Medium confidence - show warning but allow
      _showSafetyMessage('Login approved with monitoring - ${result.recommendations.first}', Colors.orange);
      await Future.delayed(const Duration(milliseconds: 2000));
      Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
      
    } else if (result.safetyLevel == SafetyLevel.low) {
      // Low confidence - require additional verification
      _showSafetyMessage('Additional verification required - Unusual patterns detected', Colors.orange);
      await Future.delayed(const Duration(milliseconds: 2000));
      Navigator.pushReplacementNamed(context, AppRoutes.behavioralAuth);
      
    } else {
      // Critical risk - deny access
      _showSafetyDialog(result);
    }
  }

  Future<void> _handleEnhancedSafetyResult(LoginSafetyResult mlResult, double anomalyScore) async {
    print('🔒 Enhanced Safety Check - ML: ${mlResult.safetyLevel}, Anomaly Score: $anomalyScore');
    
    // Combined safety assessment
    final isMLSafe = mlResult.isSafe;
    final isAnomalySafe = anomalyScore >= 0.6;
    final highConfidence = mlResult.safetyLevel == SafetyLevel.high && anomalyScore >= 0.8;
    
    if (highConfidence) {
      // Highest confidence - direct access to dashboard
      _showSafetyMessage('✅ Authentication verified - Welcome back!', Colors.green);
      await Future.delayed(const Duration(milliseconds: 1500));
      Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
      
    } else if (isMLSafe && isAnomalySafe) {
      // Both systems agree it's safe - allow with brief monitoring message
      _showSafetyMessage('🔒 Login approved - Security monitoring active', Colors.blue);
      await Future.delayed(const Duration(milliseconds: 2000));
      Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
      
    } else if (isMLSafe || isAnomalySafe) {
      // One system shows concern - require biometric or show warning
      if (await _localAuth.isBiometricEnabled()) {
        _showSafetyMessage('⚠️ Additional verification - Please use biometric authentication', Colors.orange);
        await Future.delayed(const Duration(milliseconds: 2000));
        
        // Try biometric authentication
        final biometricSuccess = await _localAuth.authenticateWithBiometrics(
          reason: 'Additional security verification required',
        );
        
        if (biometricSuccess) {
          Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
        } else {
          Navigator.pushReplacementNamed(context, AppRoutes.behavioralAuth);
        }
      } else {
        _showSafetyMessage('⚠️ Security verification required - Unusual activity detected', Colors.orange);
        await Future.delayed(const Duration(milliseconds: 2000));
        Navigator.pushReplacementNamed(context, AppRoutes.behavioralAuth);
      }
      
    } else {
      // Both systems show high concern - require full behavioral authentication
      _showSafetyMessage('🚨 Security Alert - Full verification required', Colors.red);
      await Future.delayed(const Duration(milliseconds: 2500));
      Navigator.pushReplacementNamed(context, AppRoutes.behavioralAuth);
    }
  }
  
  void _showSafetyMessage(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              color == Colors.green ? Icons.check_circle : Icons.warning,
              color: color,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: color.withOpacity(0.8),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
  
  void _showSafetyDialog(LoginSafetyResult result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E293B),
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            const SizedBox(width: 10),
            const Text(
              'High Risk Login Detected',
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Our AI security system has detected unusual behavioral patterns that may indicate unauthorized access.',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
            const SizedBox(height: 16),
            Text(
              'Safety Score: ${(result.safetyProbability * 100).toInt()}%',
              style: TextStyle(color: Colors.red, fontSize: 14, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text(
              'Confidence: ${(result.confidence * 100).toInt()}%',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
            const SizedBox(height: 16),
            Text(
              'Risk Factors:',
              style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            ...result.riskFactors.map((factor) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('• ', style: TextStyle(color: Colors.red)),
                  Expanded(
                    child: Text(
                      factor,
                      style: TextStyle(color: Colors.white70, fontSize: 13),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushReplacementNamed(context, AppRoutes.behavioralAuth);
            },
            child: const Text(
              'Additional Verification',
              style: TextStyle(color: Color(0xFF3B82F6)),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _emailController.clear();
                _passwordController.clear();
                _keystrokeTiming.clear();
                _currentBehavioralData = BehavioralData();
              });
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Deny Access'),
          ),
        ],
      ),
    );
  }
  Future<void> _recordBehavioralData() async {
  final trimmedUserId = _emailController.text.trim();
  if (trimmedUserId.isEmpty) return;

  final avgVelocity = _calculateAverageSwipeVelocity();
  print('[LoginScreen] avgSwipeVelocity: $avgVelocity');

  final sessionEndTime = DateTime.now();
    final duration = sessionEndTime.difference(_sessionStartTime!).inSeconds.toDouble();
    final Angles = _holdAngleTracker.consumeHoldAngles();

  _currentBehavioralData
    ..gyroMagnitudes = GyroTracker().consumeMagnitudes()
    ..swipeVelocities = _swipeVelocities
    ..tapPositions = _tapPositions.map((pos) => Offset(pos['x']!, pos['y']!)).toList()
    ..sessionDuration = duration
    ..holdAngles = Angles;

  _currentBehavioralData.keystrokeIntervals = _keystrokeIntervals;

  await FirebaseService().uploadBehavioralData(trimmedUserId, _currentBehavioralData);
}




  
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: constants.AppColors.debitRed,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Future<void> _initializeBiometrics() async {
    try {
      final isAvailable = await _biometricService.isBiometricAvailable();
      final availableBiometrics = await _biometricService.getAvailableBiometrics();
      
      // Check if user has registered account first
      final hasRegisteredUser = await AuthService.getUserProfile() != null;

      setState(() {
        _biometricAvailable = isAvailable && availableBiometrics.isNotEmpty && hasRegisteredUser;
        _availableBiometrics = availableBiometrics;
        
        if (!hasRegisteredUser) {
          _biometricButtonText = 'Register an account first to use biometric login';
        } else if (!isAvailable || availableBiometrics.isEmpty) {
          _biometricButtonText = 'Biometric authentication not available';
        } else {
          _biometricButtonText = 'Sign in with ${_biometricService.getBiometricTypeName(availableBiometrics)}';
        }
      });
    } catch (e) {
      print('Error initializing biometrics: $e');
    }
  }

  Future<void> _handleBiometricLogin() async {
    if (!_biometricAvailable) {
      _showError('Biometric authentication is not available');
      return;
    }

    // Check if user has registered account
    final userProfile = await AuthService.getUserProfile();
    if (userProfile == null) {
      _showError('Please register an account first');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await _biometricService.authenticateWithBiometrics(
        reason: 'Please authenticate to access your Trust Chain Banking account',
      );

      if (result.success) {
        GyroTracker().startBurst();
        
        // Biometric authentication successful - now validate account
        final success = await AuthService.login(
          userProfile['email'],
          userProfile['password'],
        );
        
        if (success) {
          await _recordBehavioralData();
          
          // Calculate trust score (higher for biometric auth)
          final trustScore = await SecurityService.calculateTrustScore();

          // Navigate to dashboard (biometric auth gets higher trust)
          Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
        } else {
          _showError('Account verification failed. Please use password login.');
        }
      } else {
        _showError(result.errorMessage ?? 'Biometric authentication failed');
        _shakeController.forward().then((_) => _shakeController.reverse());
      }
    } catch (e) {
      _showError('Biometric authentication error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showPasswordResetInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.info_outline, color: Color(0xFF3B82F6)),
              SizedBox(width: 8),
              Text(
                'Password Reset Information',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'For security reasons, banking passwords cannot be reset online.',
                style: TextStyle(color: Colors.white70, fontSize: 14),
              ),
              SizedBox(height: 12),
              Text(
                'To reset your password, please:',
                style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 8),
              Text(
                '• Visit your nearest Trust Chain Bank branch\n• Bring valid government ID\n• Fill out password reset form\n• Verify your identity with bank officials',
                style: TextStyle(color: Colors.white70, fontSize: 13),
              ),
              SizedBox(height: 12),
              Text(
                'Customer Care: 1800-123-4567',
                style: TextStyle(color: Color(0xFF3B82F6), fontSize: 14, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Understood',
                style: TextStyle(color: Color(0xFF3B82F6)),
              ),
            ),
          ],
        );
      },
    );
  }

 
  
  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
    if (notification is ScrollUpdateNotification) {
      final velocity = notification.scrollDelta!.abs() / (notification.metrics.viewportDimension);
      if (velocity > 0) {
        _swipeVelocities.add(velocity * 1000); // Normalize to px/sec
      }
    }
    return false;
  },
      child: GestureDetector(
         onPanStart: _handleSwipeStart,
         onPanEnd: _handleSwipeEnd,
             behavior: HitTestBehavior.translucent,
             onTapDown: _handleTapDown,
      
        child: Scaffold(
          backgroundColor: constants.AppColors.bankingBackground,
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  constants.AppColors.bankingBackground,
                  constants.AppColors.bankingSurface,
                ],
              ),
            ),
            child: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: AnimatedBuilder(
                  animation: _shakeAnimation,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(_shakeAnimation.value, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 40),
                          
                          // Header
                          _buildHeader(),
                          const SizedBox(height: 48),
                          
                          // Login Form
                          _buildLoginForm(),
                          const SizedBox(height: 32),
                          
                          // Login Button
                          _buildLoginButton(),
                          const SizedBox(height: 24),
                          
                          // Additional Options
                          _buildAdditionalOptions(),
                          const SizedBox(height: 32),
                          
                          // Security Notice
                          _buildSecurityNotice(),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [AppColors.primary, AppColors.primaryDark],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: const Icon(
            Icons.account_balance,
            size: 40,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 24),
        
        // Welcome text
        Text(
          'Welcome Back',
          style: AppTextStyles.heading1.copyWith(
            fontSize: 28,
            fontWeight: FontWeight.w800,
          ),
        ),
        const SizedBox(height: 8),
        
        Text(
          'Sign in to access your secure banking',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
  
  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // Email Field
          _buildEmailField(),
          const SizedBox(height: 20),
          
          // Password Field
          _buildPasswordField(),
          const SizedBox(height: 16),
          
          // Remember Me & Forgot Password
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Checkbox(
                      value: _rememberMe,
                      onChanged: (value) => setState(() => _rememberMe = value ?? false),
                      activeColor: AppColors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    Text(
                      'Remember me',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () {
                  _showPasswordResetInfo(context);
                },
                child: const Text(
                  'Password Help?',
                  style: TextStyle(
                    color: Color(0xFF3B82F6),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      onTap: () => GyroTracker().startBurst(),
      onChanged: (value) {
    final now = DateTime.now();
    if (_lastKeyTime != null) {
      final diff = now.difference(_lastKeyTime!).inMilliseconds;
      _keystrokeIntervals.add(diff);
    }
    _lastKeyTime = now;
  },
      style: AppTextStyles.bodyLarge,
      decoration: InputDecoration(
        labelText: 'Email Address',
        labelStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textSecondary,
        ),
        prefixIcon: Icon(
          Icons.email_outlined,
          color: AppColors.textSecondary,
        ),
        filled: true,
        fillColor: AppColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.error, width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your email';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'Please enter a valid email';
        }
        return null;
      },
    );
  }
  
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      onTap: () => GyroTracker().startBurst(),
      onChanged: (value) {
    final now = DateTime.now();
    if (_lastKeyTime != null) {
      final diff = now.difference(_lastKeyTime!).inMilliseconds;
      _keystrokeIntervals.add(diff);
    }
    _lastKeyTime = now;
  },
      style: AppTextStyles.bodyLarge,
      decoration: InputDecoration(
        labelText: 'Password',
        labelStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textSecondary,
        ),
        prefixIcon: Icon(
          Icons.lock_outlined,
          color: AppColors.textSecondary,
        ),
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            color: AppColors.textSecondary,
          ),
          onPressed: () {
            setState(() => _isPasswordVisible = !_isPasswordVisible);
          },
        ),
        filled: true,
        fillColor: AppColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.error, width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your password';
        }
        if (value.length < 6) {
          return 'Password must be at least 6 characters';
        }
        return null;
      },
    );
  }
Widget _buildLoginButton() {
  return AppButton(
    text: 'Sign In',
    onPressed: _isLoading ? null : () async {
      final ready = await LocationPermissionService.ensureLocationReady();

      if (!ready) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Please enable GPS and grant location permission to continue.'),
            action: SnackBarAction(
              label: 'Settings',
              onPressed: () => Geolocator.openLocationSettings(),
            ),
          ),
        );
        return;
      }

      await _handleLogin();
    },
    isLoading: _isLoading,
    icon: Icons.login,
  );
}

  Widget _buildAdditionalOptions() {
    return Column(
      children: [
        // Biometric Login
        if (_biometricAvailable)
          OutlinedButton.icon(
            onPressed: _isLoading ? null : _handleBiometricLogin,
            icon: Icon(
              _availableBiometrics.contains(BiometricType.face)
                  ? Icons.face
                  : Icons.fingerprint,
            ),
            label: Text(_biometricButtonText),
            style: OutlinedButton.styleFrom(
              foregroundColor: constants.AppColors.bankingPrimary,
              side: BorderSide(color: constants.AppColors.bankingPrimary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        
        // Show info when biometrics not available due to no account
        if (!_biometricAvailable && _biometricButtonText.contains('Register'))
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.textSecondary, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Create an account to unlock biometric login',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        const SizedBox(height: 16),
        
        // Divider
        Row(
          children: [
            Expanded(child: Divider(color: AppColors.divider)),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Don\'t have an account?',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            Expanded(child: Divider(color: AppColors.divider)),
          ],
        ),
        const SizedBox(height: 16),
        
        // Register Button
        TextButton(
          onPressed: () {
            Navigator.pushNamed(context, AppRoutes.register);
          },
          child: Text(
            'Create Account',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildSecurityNotice() {
    return AppCard(
      backgroundColor: AppColors.primary.withOpacity(0.1),
      child: Row(
        children: [
          Icon(
            Icons.psychology,
            color: AppColors.primary,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Your login is protected by AI-powered behavioral analysis and machine learning security.',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

@override
void dispose() {
  _emailController.dispose();
  _passwordController.dispose();
  _shakeController.dispose();
  GyroTracker().stop();
  _holdAngleTracker.stop();

  if (!_hasUploadedBehavior) {
    _recordBehavioralData();
    _hasUploadedBehavior = true;
  }

  super.dispose();
}

}
