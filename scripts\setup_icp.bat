@echo off
REM Trust Chain Banking - ICP Setup Script for Windows

echo 🚀 Setting up ICP Canister for Trust Chain Banking...

REM Check if dfx is installed
where dfx >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ DFX not found. Please install DFX manually from https://internetcomputer.org/docs/current/developer-docs/setup/install
    echo Run: sh -ci "$(curl -fsSL https://internetcomputer.org/install.sh)"
    pause
    exit /b 1
)

REM Navigate to ICP config directory
cd icp_config

REM Start local replica
echo 🔗 Starting local ICP replica...
start /b dfx start --clean

REM Wait for replica to start
timeout /t 10

REM Deploy the canister
echo ⛓️ Deploying behavioral authentication canister...
dfx deploy trust_chain_behavioral_auth

REM Get canister ID
for /f "tokens=*" %%i in ('dfx canister id trust_chain_behavioral_auth') do set CANISTER_ID=%%i

echo ✅ Canister deployed successfully!
echo 📋 Canister ID: %CANISTER_ID%

echo 🎉 ICP setup complete!
echo    Canister ID: %CANISTER_ID%
echo    Local replica: http://localhost:4943
echo    Ready for blockchain behavioral authentication!

pause
