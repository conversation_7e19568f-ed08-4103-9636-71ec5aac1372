// Trust Chain Banking - Masumi Differential Privacy Configuration
// Complete setup for privacy-preserving behavioral analytics

import {
  PrivacyEngine,
  LaplaceMechanism,
  GaussianMechanism,
  ExponentialMechanism,
  DifferentialPrivacyConfig
} from '@masumi/privacy-core';

class TrustChainMasumiConfig {
  static readonly EPSILON = 1.0;  // Privacy budget
  static readonly DELTA = 1e-5;   // Failure probability
  static readonly SENSITIVITY = 1.0;  // Query sensitivity

  // Differential Privacy Mechanisms
  static readonly laplaceMechanism = new LaplaceMechanism({
    epsilon: TrustChainMasumiConfig.EPSILON,
    sensitivity: TrustChainMasumiConfig.SENSITIVITY
  });

  static readonly gaussianMechanism = new GaussianMechanism({
    epsilon: TrustChainMasumiConfig.EPSILON,
    delta: TrustChainMasumiConfig.DELTA,
    sensitivity: TrustChainMasumiConfig.SENSITIVITY
  });

  // Privacy Engine Configuration
  static readonly config: DifferentialPrivacyConfig = {
    epsilon: TrustChainMasumiConfig.EPSILON,
    delta: TrustChainMasumiConfig.DELTA,
    mechanisms: {
      behavioral_patterns: TrustChainMasumiConfig.laplaceMechanism,
      transaction_analytics: TrustChainMasumiConfig.gaussianMechanism,
      risk_assessment: TrustChainMasumiConfig.laplaceMechanism
    },
    // Composition strategies
    composition: {
      type: 'advanced',
      allowParallel: true,
      maxQueries: 1000
    }
  };

  // Behavioral Data Privacy Transformations
  static readonly behavioralTransforms = {
    keystrokeTimings: {
      mechanism: 'laplace',
      epsilon: 0.1,
      clipBounds: [0, 1000]  // ms
    },
    touchPressure: {
      mechanism: 'gaussian',
      epsilon: 0.1,
      delta: 1e-6,
      clipBounds: [0, 1]
    },
    deviceOrientation: {
      mechanism: 'laplace',
      epsilon: 0.05,
      clipBounds: [-180, 180]  // degrees
    },
    biometricFeatures: {
      mechanism: 'exponential',
      epsilon: 0.2,
      qualityFunction: 'entropy'
    }
  };

  // Privacy Budgeting
  static readonly budgetAllocation = {
    authentication: 0.3,
    riskAnalysis: 0.3,
    behavioralProfiling: 0.2,
    fraudDetection: 0.2
  };
}

export default TrustChainMasumiConfig;
