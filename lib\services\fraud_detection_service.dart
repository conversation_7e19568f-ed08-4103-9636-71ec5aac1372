import 'dart:async';
import 'dart:math' as math;

class FraudDetectionService {
  static final FraudDetectionService _instance = FraudDetectionService._internal();
  factory FraudDetectionService() => _instance;
  FraudDetectionService._internal();

  final math.Random _random = math.Random();
  bool _isInitialized = false;
  
  // Fraud detection parameters
  final List<TransactionPattern> _transactionHistory = [];
  final List<DeviceFingerprint> _knownDevices = [];
  final List<LocationPattern> _locationHistory = [];
  final List<BehavioralPattern> _behavioralBaseline = [];
  
  // Risk scoring weights
  static const double _transactionAmountWeight = 0.25;
  static const double _locationWeight = 0.20;
  static const double _timePatternWeight = 0.15;
  static const double _deviceWeight = 0.20;
  static const double _behavioralWeight = 0.20;

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    print('🛡️ Initializing Fraud Detection Service...');
    
    // Load historical patterns
    await _loadHistoricalPatterns();
    
    // Initialize ML models (simulated)
    await _initializeMlModels();
    
    // Setup real-time monitoring
    _setupRealtimeMonitoring();
    
    _isInitialized = true;
    print('✅ Fraud Detection Service initialized successfully');
  }

  Future<void> _loadHistoricalPatterns() async {
    // Simulate loading user's historical transaction patterns
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Generate sample transaction patterns
    for (int i = 0; i < 50; i++) {
      _transactionHistory.add(TransactionPattern(
        amount: _random.nextDouble() * 100000,
        timestamp: DateTime.now().subtract(Duration(days: _random.nextInt(365))),
        category: TransactionCategory.values[_random.nextInt(TransactionCategory.values.length)],
        location: _generateRandomLocation(),
        deviceId: 'device_${_random.nextInt(3)}',
      ));
    }
    
    // Generate behavioral baseline
    for (int i = 0; i < 20; i++) {
      _behavioralBaseline.add(BehavioralPattern(
        typingSpeed: 45 + _random.nextDouble() * 20,
        dwellTime: 100 + _random.nextDouble() * 50,
        pressure: 0.7 + _random.nextDouble() * 0.3,
        timestamp: DateTime.now().subtract(Duration(days: _random.nextInt(30))),
      ));
    }
  }

  Future<void> _initializeMlModels() async {
    // Simulate ML model initialization
    await Future.delayed(const Duration(milliseconds: 500));
    
    print('🤖 Fraud detection ML models loaded:');
    print('   - Transaction Anomaly Detection Model');
    print('   - Geolocation Risk Assessment Model');
    print('   - Behavioral Biometrics Model');
    print('   - Device Fingerprinting Model');
  }

  void _setupRealtimeMonitoring() {
    // Real-time transaction monitoring
    Timer.periodic(const Duration(seconds: 30), (timer) {
      _performFraudScan();
    });
  }

  void _performFraudScan() {
    // Simulate periodic fraud detection scans
    if (_random.nextDouble() < 0.05) { // 5% chance of detecting suspicious activity
      print('⚠️ Suspicious activity pattern detected in real-time monitoring');
    }
  }

  Future<double> calculateRiskScore({
    double? transactionAmount,
    String? location,
    String? deviceId,
    Map<String, dynamic>? behavioralData,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    print('🔍 Calculating fraud risk score...');

    double totalRisk = 0.0;

    // Transaction amount risk
    if (transactionAmount != null) {
      final amountRisk = _calculateTransactionAmountRisk(transactionAmount);
      totalRisk += amountRisk * _transactionAmountWeight;
      print('   Transaction Amount Risk: ${(amountRisk * 100).toStringAsFixed(1)}%');
    }

    // Location risk
    if (location != null) {
      final locationRisk = _calculateLocationRisk(location);
      totalRisk += locationRisk * _locationWeight;
      print('   Location Risk: ${(locationRisk * 100).toStringAsFixed(1)}%');
    }

    // Device risk
    if (deviceId != null) {
      final deviceRisk = _calculateDeviceRisk(deviceId);
      totalRisk += deviceRisk * _deviceWeight;
      print('   Device Risk: ${(deviceRisk * 100).toStringAsFixed(1)}%');
    }

    // Behavioral risk
    if (behavioralData != null) {
      final behavioralRisk = _calculateBehavioralRisk(behavioralData);
      totalRisk += behavioralRisk * _behavioralWeight;
      print('   Behavioral Risk: ${(behavioralRisk * 100).toStringAsFixed(1)}%');
    }

    // Time pattern risk
    final timeRisk = _calculateTimePatternRisk();
    totalRisk += timeRisk * _timePatternWeight;
    print('   Time Pattern Risk: ${(timeRisk * 100).toStringAsFixed(1)}%');

    // Normalize to 0-1 range
    totalRisk = math.min(1.0, math.max(0.0, totalRisk));

    print('🎯 Total Fraud Risk Score: ${(totalRisk * 100).toStringAsFixed(1)}%');

    return totalRisk;
  }

  double _calculateTransactionAmountRisk(double amount) {
    // Calculate risk based on transaction amount
    final avgAmount = _transactionHistory.isNotEmpty
        ? _transactionHistory.map((t) => t.amount).reduce((a, b) => a + b) / _transactionHistory.length
        : 10000;

    final stdDev = _calculateStandardDeviation(_transactionHistory.map((t) => t.amount).toList());
    
    // Z-score calculation
    final zScore = (amount - avgAmount) / (stdDev + 1);
    
    // Higher amounts have higher risk
    if (amount > avgAmount * 3) {
      return 0.8 + _random.nextDouble() * 0.2; // 80-100% risk for very high amounts
    } else if (amount > avgAmount * 2) {
      return 0.5 + _random.nextDouble() * 0.3; // 50-80% risk for high amounts
    } else if (amount > avgAmount) {
      return 0.2 + _random.nextDouble() * 0.3; // 20-50% risk for above average
    } else {
      return _random.nextDouble() * 0.2; // 0-20% risk for normal amounts
    }
  }

  double _calculateLocationRisk(String location) {
    // Check if location matches known safe locations
    final knownSafeLocations = ['home', 'office', 'usual_bank_branch'];
    
    if (knownSafeLocations.contains(location.toLowerCase())) {
      return 0.05 + _random.nextDouble() * 0.1; // Very low risk
    }
    
    // Check location history
    final locationFrequency = _locationHistory.where((l) => l.location == location).length;
    
    if (locationFrequency > 5) {
      return 0.1 + _random.nextDouble() * 0.2; // Low risk for frequent locations
    } else if (locationFrequency > 0) {
      return 0.3 + _random.nextDouble() * 0.3; // Medium risk for occasional locations
    } else {
      return 0.6 + _random.nextDouble() * 0.4; // High risk for new locations
    }
  }

  double _calculateDeviceRisk(String deviceId) {
    // Check if device is recognized
    final isKnownDevice = _knownDevices.any((d) => d.deviceId == deviceId);
    
    if (isKnownDevice) {
      return 0.05 + _random.nextDouble() * 0.1; // Very low risk for known devices
    } else {
      return 0.7 + _random.nextDouble() * 0.3; // High risk for unknown devices
    }
  }

  double _calculateBehavioralRisk(Map<String, dynamic> behavioralData) {
    if (_behavioralBaseline.isEmpty) {
      return 0.5; // Medium risk if no baseline
    }

    // Extract behavioral metrics
    final typingSpeed = behavioralData['typing_speed'] as double? ?? 0.0;
    final dwellTime = behavioralData['dwell_time'] as double? ?? 0.0;
    final pressure = behavioralData['pressure'] as double? ?? 0.0;

    // Calculate deviations from baseline
    final avgTypingSpeed = _behavioralBaseline.map((b) => b.typingSpeed).reduce((a, b) => a + b) / _behavioralBaseline.length;
    final avgDwellTime = _behavioralBaseline.map((b) => b.dwellTime).reduce((a, b) => a + b) / _behavioralBaseline.length;
    final avgPressure = _behavioralBaseline.map((b) => b.pressure).reduce((a, b) => a + b) / _behavioralBaseline.length;

    // Calculate normalized deviations
    final typingSpeedDeviation = (typingSpeed - avgTypingSpeed).abs() / avgTypingSpeed;
    final dwellTimeDeviation = (dwellTime - avgDwellTime).abs() / avgDwellTime;
    final pressureDeviation = (pressure - avgPressure).abs() / avgPressure;

    // Combine deviations
    final totalDeviation = (typingSpeedDeviation + dwellTimeDeviation + pressureDeviation) / 3;

    // Convert to risk score
    return math.min(1.0, totalDeviation * 2); // Scale up deviation to risk
  }

  double _calculateTimePatternRisk() {
    final now = DateTime.now();
    final hour = now.hour;
    
    // Higher risk for unusual hours
    if (hour >= 2 && hour <= 5) {
      return 0.8 + _random.nextDouble() * 0.2; // Very high risk for 2-5 AM
    } else if (hour >= 22 || hour <= 1) {
      return 0.4 + _random.nextDouble() * 0.3; // Medium-high risk for late night
    } else if (hour >= 6 && hour <= 22) {
      return 0.05 + _random.nextDouble() * 0.15; // Low risk for normal hours
    } else {
      return 0.2 + _random.nextDouble() * 0.3; // Medium risk for other times
    }
  }

  double _calculateStandardDeviation(List<double> values) {
    if (values.isEmpty) return 0.0;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final squaredDiffs = values.map((value) => math.pow(value - mean, 2));
    final variance = squaredDiffs.reduce((a, b) => a + b) / values.length;
    
    return math.sqrt(variance);
  }

  String _generateRandomLocation() {
    final locations = ['mumbai', 'delhi', 'bangalore', 'hyderabad', 'chennai', 'pune', 'kolkata'];
    return locations[_random.nextInt(locations.length)];
  }

  // Real-time fraud monitoring
  Future<FraudAlert?> analyzeTransaction(TransactionData transaction) async {
    final riskScore = await calculateRiskScore(
      transactionAmount: transaction.amount,
      location: transaction.location,
      deviceId: transaction.deviceId,
      behavioralData: transaction.behavioralData,
    );

    if (riskScore > 0.8) {
      return FraudAlert(
        severity: FraudAlertSeverity.critical,
        message: 'CRITICAL: Very high fraud probability detected',
        riskScore: riskScore,
        recommendedAction: 'Block transaction and require manual verification',
        timestamp: DateTime.now(),
      );
    } else if (riskScore > 0.6) {
      return FraudAlert(
        severity: FraudAlertSeverity.high,
        message: 'HIGH RISK: Transaction requires additional verification',
        riskScore: riskScore,
        recommendedAction: 'Request additional authentication',
        timestamp: DateTime.now(),
      );
    } else if (riskScore > 0.4) {
      return FraudAlert(
        severity: FraudAlertSeverity.medium,
        message: 'MEDIUM RISK: Monitor transaction closely',
        riskScore: riskScore,
        recommendedAction: 'Enhanced monitoring',
        timestamp: DateTime.now(),
      );
    }

    return null; // No alert needed for low risk
  }

  // Machine Learning model updates
  void updateUserProfile(TransactionData transaction, bool isFraudulent) {
    // In real implementation, this would update ML models
    print('📚 Updating fraud detection models with new transaction data');
    
    if (!isFraudulent) {
      // Add to safe patterns
      _transactionHistory.add(TransactionPattern(
        amount: transaction.amount,
        timestamp: DateTime.now(),
        category: transaction.category,
        location: transaction.location,
        deviceId: transaction.deviceId,
      ));
    }
  }

  Map<String, dynamic> getFraudStatistics() {
    final now = DateTime.now();
    final last30Days = _transactionHistory
        .where((t) => now.difference(t.timestamp).inDays <= 30);

    return {
      'total_transactions_30d': last30Days.length,
      'avg_transaction_amount': last30Days.isNotEmpty
          ? last30Days.map((t) => t.amount).reduce((a, b) => a + b) / last30Days.length
          : 0.0,
      'unique_locations_30d': last30Days.map((t) => t.location).toSet().length,
      'fraud_detection_accuracy': 94.7, // Simulated accuracy
      'false_positive_rate': 2.1, // Simulated false positive rate
      'model_version': '2.3.1',
      'last_model_update': '2025-07-10',
    };
  }
}

// Supporting classes
enum TransactionCategory { transfer, payment, withdrawal, investment, other }
enum FraudAlertSeverity { low, medium, high, critical }

class TransactionPattern {
  final double amount;
  final DateTime timestamp;
  final TransactionCategory category;
  final String location;
  final String deviceId;

  TransactionPattern({
    required this.amount,
    required this.timestamp,
    required this.category,
    required this.location,
    required this.deviceId,
  });
}

class DeviceFingerprint {
  final String deviceId;
  final String deviceModel;
  final String osVersion;
  final DateTime firstSeen;
  final bool isTrusted;

  DeviceFingerprint({
    required this.deviceId,
    required this.deviceModel,
    required this.osVersion,
    required this.firstSeen,
    required this.isTrusted,
  });
}

class LocationPattern {
  final String location;
  final DateTime timestamp;
  final int frequency;

  LocationPattern({
    required this.location,
    required this.timestamp,
    required this.frequency,
  });
}

class BehavioralPattern {
  final double typingSpeed;
  final double dwellTime;
  final double pressure;
  final DateTime timestamp;

  BehavioralPattern({
    required this.typingSpeed,
    required this.dwellTime,
    required this.pressure,
    required this.timestamp,
  });
}

class TransactionData {
  final double amount;
  final String location;
  final String deviceId;
  final TransactionCategory category;
  final Map<String, dynamic> behavioralData;
  final DateTime timestamp;

  TransactionData({
    required this.amount,
    required this.location,
    required this.deviceId,
    required this.category,
    required this.behavioralData,
    required this.timestamp,
  });
}

class FraudAlert {
  final FraudAlertSeverity severity;
  final String message;
  final double riskScore;
  final String recommendedAction;
  final DateTime timestamp;

  FraudAlert({
    required this.severity,
    required this.message,
    required this.riskScore,
    required this.recommendedAction,
    required this.timestamp,
  });
}
