// Trust Chain Behavioral Authentication Canister
// Stores behavioral hashes on ICP blockchain for verification

import Map "mo:base/Map";
import Time "mo:base/Time";
import Text "mo:base/Text";
import Array "mo:base/Array";
import Result "mo:base/Result";

actor Trust<PERSON>hainBehavioralAuth {
    
    // Data Types
    type BehavioralRecord = {
        userPrincipal: Text;
        behavioralHash: Text;
        trustScore: Float;
        timestamp: Int;
        sessionId: Text;
        verified: Bool;
    };

    type VerificationResult = {
        isVerified: Bool;
        trustScore: Float;
        recordCount: Nat;
        lastUpdate: Int;
    };

    // Storage
    private stable var records : [(Text, BehavioralRecord)] = [];
    private var behavioralDB = Map.fromIter<Text, BehavioralRecord>(records.vals(), records.size(), Text.equal, Text.hash);

    // Store behavioral data hash
    public func storeBehavioralData(
        userPrincipal: Text,
        behavioralHash: Text,
        trustScore: Float,
        sessionId: Text
    ) : async Text {
        let timestamp = Time.now();
        let transactionId = "icp_tx_" # Int.toText(timestamp);
        
        let record: BehavioralRecord = {
            userPrincipal = userPrincipal;
            behavioralHash = behavioralHash;
            trustScore = trustScore;
            timestamp = timestamp;
            sessionId = sessionId;
            verified = true;
        };

        behavioralDB.put(transactionId, record);
        
        transactionId
    };

    // Verify behavioral authentication
    public query func verifyBehavioralAuthentication(
        userPrincipal: Text,
        currentHash: Text
    ) : async VerificationResult {
        
        var totalTrustScore: Float = 0;
        var recordCount: Nat = 0;
        var lastUpdate: Int = 0;
        var isVerified = false;

        // Check recent records for this user
        for ((_, record) in behavioralDB.entries()) {
            if (record.userPrincipal == userPrincipal) {
                totalTrustScore += record.trustScore;
                recordCount += 1;
                
                if (record.timestamp > lastUpdate) {
                    lastUpdate := record.timestamp;
                };

                // Check if current hash matches any recent pattern
                if (record.behavioralHash == currentHash) {
                    isVerified := true;
                };
            };
        };

        let avgTrustScore = if (recordCount > 0) {
            totalTrustScore / Float.fromInt(recordCount);
        } else { 0.0 };

        {
            isVerified = isVerified or (avgTrustScore > 0.7);
            trustScore = avgTrustScore;
            recordCount = recordCount;
            lastUpdate = lastUpdate;
        }
    };

    // Get canister health
    public query func getCanisterHealth() : async {
        status: Text;
        recordCount: Nat;
        lastActivity: Int;
    } {
        {
            status = "healthy";
            recordCount = behavioralDB.size();
            lastActivity = Time.now();
        }
    };

    // System functions
    system func preupgrade() {
        records := Array.tabulate(behavioralDB.size(), func(i: Nat): (Text, BehavioralRecord) {
            let entry = behavioralDB.entries().nth(i);
            switch(entry) {
                case (?kv) kv;
                case null ("", {
                    userPrincipal = "";
                    behavioralHash = "";
                    trustScore = 0.0;
                    timestamp = 0;
                    sessionId = "";
                    verified = false;
                });
            }
        });
    };

    system func postupgrade() {
        behavioralDB := Map.fromIter<Text, BehavioralRecord>(records.vals(), records.size(), Text.equal, Text.hash);
    };
}
