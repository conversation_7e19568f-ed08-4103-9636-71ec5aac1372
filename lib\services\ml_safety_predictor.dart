import 'dart:math' as math;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/behavioral_data_model.dart';

/// ML-based login safety prediction service
/// Predicts whether a login attempt is safe based on behavioral patterns
class MLSafetyPredictor {
  static const String _keyHistoricalData = 'ml_historical_data';
  static const String _keyUserBaseline = 'ml_user_baseline';
  static const String _keyModelParams = 'ml_model_params';
  
  static SharedPreferences? _prefs;
  
  static Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Predict login safety based on current behavioral data
  /// Returns a LoginSafetyResult with probability and confidence scores
  static Future<LoginSafetyResult> predictLoginSafety({
    required BehavioralData currentData,
    required String userId,
    required String deviceInfo,
    required DateTime loginTime,
    String? locationContext,
  }) async {
    await initialize();
    
    try {
      // Extract features from current session
      final features = _extractFeatures(
        currentData: currentData,
        loginTime: loginTime,
        deviceInfo: deviceInfo,
        locationContext: locationContext,
      );
      
      // Load user's historical baseline
      final baseline = await _loadUserBaseline(userId);
      
      // Calculate anomaly scores for different behavioral aspects
      final keystrokeAnomalyScore = _calculateKeystrokeAnomaly(features, baseline);
      final timingAnomalyScore = _calculateTimingAnomaly(features, baseline);
      final sessionAnomalyScore = _calculateSessionAnomaly(features, baseline);
      final deviceAnomalyScore = _calculateDeviceAnomaly(features, baseline);
      
      // Weighted ensemble prediction (simulating trained ML model)
      final safetyProbability = _ensemblePrediction(
        keystrokeScore: keystrokeAnomalyScore,
        timingScore: timingAnomalyScore,
        sessionScore: sessionAnomalyScore,
        deviceScore: deviceAnomalyScore,
      );
      
      // Calculate prediction confidence
      final confidence = _calculateConfidence(
        safetyProbability: safetyProbability,
        dataQuality: _assessDataQuality(features),
        baselineStrength: baseline != null ? baseline['strength'] ?? 0.5 : 0.5,
      );
      
      // Determine safety level and recommendations
      final safetyLevel = _determineSafetyLevel(safetyProbability);
      final recommendations = _generateRecommendations(safetyLevel, features);
      
      // Update user baseline with current data (for continuous learning)
      await _updateUserBaseline(userId, features);
      
      return LoginSafetyResult(
        isSafe: safetyProbability >= 0.65,
        safetyProbability: safetyProbability,
        confidence: confidence,
        safetyLevel: safetyLevel,
        anomalyScores: {
          'keystroke': keystrokeAnomalyScore,
          'timing': timingAnomalyScore,
          'session': sessionAnomalyScore,
          'device': deviceAnomalyScore,
        },
        recommendations: recommendations,
        riskFactors: _identifyRiskFactors(features, baseline),
        timestamp: DateTime.now(),
      );
      
    } catch (e) {
      print('ML Prediction Error: $e');
      // Return conservative result on error
      return LoginSafetyResult(
        isSafe: false,
        safetyProbability: 0.5,
        confidence: 0.3,
        safetyLevel: SafetyLevel.medium,
        anomalyScores: {},
        recommendations: ['Manual verification recommended due to analysis error'],
        riskFactors: ['Prediction system error'],
        timestamp: DateTime.now(),
      );
    }
  }

  /// Extract comprehensive features from behavioral data
  static Map<String, dynamic> _extractFeatures({
    required BehavioralData currentData,
    required DateTime loginTime,
    required String deviceInfo,
    String? locationContext,
  }) {
    return {
      // Keystroke features
      'keystroke_count': currentData.keystrokeTimes.length,
      'keystroke_intervals': currentData.keystrokeTimes.length > 1 
          ? _calculateIntervals(currentData.keystrokeTimes)
          : [],
      'keystroke_variance': currentData.keystrokeTimes.length > 2
          ? _calculateVariance(currentData.keystrokeTimes.map((e) => e.toDouble()).toList())
          : 0.0,
      
      // Session features
      'session_duration': DateTime.now().millisecondsSinceEpoch - currentData.sessionStartTime,
      'login_hour': loginTime.hour,
      'login_day_of_week': loginTime.weekday,
      'login_timestamp': loginTime.millisecondsSinceEpoch,
      
      // Device features
      'device_info': deviceInfo,
      'location_context': locationContext ?? 'unknown',
      
      // Interaction patterns
      'interaction_intensity': _calculateInteractionIntensity(currentData),
      'session_consistency': _calculateSessionConsistency(currentData),
      
      // Temporal features
      'time_since_last_login': _getTimeSinceLastLogin(),
      'login_frequency_score': _calculateLoginFrequencyScore(),
    };
  }

  /// Calculate keystroke anomaly score
  static double _calculateKeystrokeAnomaly(Map<String, dynamic> features, Map<String, dynamic>? baseline) {
    if (baseline == null || !baseline.containsKey('keystroke_patterns')) {
      return 0.5; // Neutral score for new users
    }
    
    final currentTypingSpeed = features['typing_speed'] ?? 0.0;
    final currentVariance = features['keystroke_variance'] ?? 0.0;
    
    final baselineSpeed = baseline['keystroke_patterns']['avg_typing_speed'] ?? 50.0;
    final baselineVariance = baseline['keystroke_patterns']['avg_variance'] ?? 100.0;
    
    // Calculate deviation from baseline
    final speedDeviation = (currentTypingSpeed - baselineSpeed).abs() / (baselineSpeed + 1);
    final varianceDeviation = (currentVariance - baselineVariance).abs() / (baselineVariance + 1);
    
    // Combine deviations (lower deviation = higher safety)
    final anomalyScore = 1.0 - ((speedDeviation + varianceDeviation) / 2).clamp(0.0, 1.0);
    
    return anomalyScore;
  }

  /// Calculate timing anomaly score
  static double _calculateTimingAnomaly(Map<String, dynamic> features, Map<String, dynamic>? baseline) {
    if (baseline == null || !baseline.containsKey('timing_patterns')) {
      return 0.5;
    }
    
    final currentHour = features['login_hour'] ?? 12;
    final currentDayOfWeek = features['login_day_of_week'] ?? 3;
    
    final baselineHours = (baseline['timing_patterns']['typical_hours'] as List? ?? []).cast<int>();
    final baselineDays = (baseline['timing_patterns']['typical_days'] as List? ?? []).cast<int>();
    
    // Check if current time matches typical patterns
    final hourMatch = baselineHours.contains(currentHour) ? 1.0 : 0.3;
    final dayMatch = baselineDays.contains(currentDayOfWeek) ? 1.0 : 0.7;
    
    return (hourMatch + dayMatch) / 2;
  }

  /// Calculate session anomaly score
  static double _calculateSessionAnomaly(Map<String, dynamic> features, Map<String, dynamic>? baseline) {
    if (baseline == null || !baseline.containsKey('session_patterns')) {
      return 0.5;
    }
    
    final sessionDuration = features['session_duration'] ?? 0;
    final interactionIntensity = features['interaction_intensity'] ?? 0.5;
    
    final baselineDuration = baseline['session_patterns']['avg_duration'] ?? 30000;
    final baselineIntensity = baseline['session_patterns']['avg_intensity'] ?? 0.5;
    
    // Calculate session pattern similarity
    final durationScore = 1.0 - ((sessionDuration - baselineDuration).abs() / (baselineDuration + 1000)).clamp(0.0, 1.0);
    final intensityScore = 1.0 - (interactionIntensity - baselineIntensity).abs();
    
    return (durationScore + intensityScore) / 2;
  }

  /// Calculate device anomaly score
  static double _calculateDeviceAnomaly(Map<String, dynamic> features, Map<String, dynamic>? baseline) {
    if (baseline == null || !baseline.containsKey('device_patterns')) {
      return 0.8; // High trust for first time on device
    }
    
    final currentDevice = features['device_info'] ?? '';
    final currentLocation = features['location_context'] ?? '';
    
    final knownDevices = (baseline['device_patterns']['known_devices'] as List? ?? []).cast<String>();
    final knownLocations = (baseline['device_patterns']['known_locations'] as List? ?? []).cast<String>();
    
    final deviceMatch = knownDevices.contains(currentDevice) ? 1.0 : 0.4;
    final locationMatch = knownLocations.contains(currentLocation) ? 1.0 : 0.6;
    
    return (deviceMatch + locationMatch) / 2;
  }

  /// Ensemble prediction using weighted combination
  static double _ensemblePrediction({
    required double keystrokeScore,
    required double timingScore,
    required double sessionScore,
    required double deviceScore,
  }) {
    // Weighted combination based on feature importance
    const weights = {
      'keystroke': 0.35,  // Highest weight for behavioral patterns
      'timing': 0.25,     // Important for habit patterns
      'session': 0.25,    // Session behavior patterns
      'device': 0.15,     // Device/location context
    };
    
    final weightedScore = 
        (keystrokeScore * weights['keystroke']!) +
        (timingScore * weights['timing']!) +
        (sessionScore * weights['session']!) +
        (deviceScore * weights['device']!);
    
    return weightedScore.clamp(0.0, 1.0);
  }

  /// Calculate prediction confidence
  static double _calculateConfidence({
    required double safetyProbability,
    required double dataQuality,
    required double baselineStrength,
  }) {
    // Confidence is higher when:
    // 1. We have quality data
    // 2. Strong user baseline exists
    // 3. Prediction is not ambiguous (close to 0 or 1)
    
    final ambiguityPenalty = 1.0 - (0.5 - (safetyProbability - 0.5).abs()) * 2;
    final confidence = (dataQuality * 0.4) + (baselineStrength * 0.4) + (ambiguityPenalty * 0.2);
    
    return confidence.clamp(0.0, 1.0);
  }

  /// Determine safety level from probability
  static SafetyLevel _determineSafetyLevel(double safetyProbability) {
    if (safetyProbability >= 0.85) return SafetyLevel.high;
    if (safetyProbability >= 0.65) return SafetyLevel.medium;
    if (safetyProbability >= 0.35) return SafetyLevel.low;
    return SafetyLevel.critical;
  }

  /// Generate actionable recommendations
  static List<String> _generateRecommendations(SafetyLevel level, Map<String, dynamic> features) {
    switch (level) {
      case SafetyLevel.high:
        return ['Login approved - normal behavioral patterns detected'];
      case SafetyLevel.medium:
        return [
          'Login approved with monitoring',
          'Consider enabling additional verification for enhanced security'
        ];
      case SafetyLevel.low:
        return [
          'Unusual patterns detected - additional verification recommended',
          'Monitor for suspicious activity',
          'Consider updating security settings'
        ];
      case SafetyLevel.critical:
        return [
          'High risk login attempt detected',
          'Immediate security review required',
          'Consider temporarily locking account',
          'Contact security team if unauthorized'
        ];
    }
  }

  /// Identify specific risk factors
  static List<String> _identifyRiskFactors(Map<String, dynamic> features, Map<String, dynamic>? baseline) {
    final riskFactors = <String>[];
    
    // Check unusual timing
    final loginHour = features['login_hour'] ?? 12;
    if (loginHour < 6 || loginHour > 23) {
      riskFactors.add('Unusual login time');
    }
    
    // Check typing patterns
    final typingSpeed = features['typing_speed'] ?? 0.0;
    if (typingSpeed > 120 || typingSpeed < 10) {
      riskFactors.add('Abnormal typing speed');
    }
    
    // Check session duration
    final sessionDuration = features['session_duration'] ?? 0;
    if (sessionDuration < 5000) {
      riskFactors.add('Very short session duration');
    }
    
    // Check device consistency
    if (baseline != null) {
      final deviceInfo = features['device_info'] ?? '';
      final knownDevices = (baseline['device_patterns']?['known_devices'] as List? ?? []);
      if (!knownDevices.contains(deviceInfo)) {
        riskFactors.add('Unknown device');
      }
    }
    
    return riskFactors;
  }

  /// Helper methods
  static List<int> _calculateIntervals(List<int> times) {
    if (times.length < 2) return [];
    return List.generate(times.length - 1, (i) => times[i + 1] - times[i]);
  }

  static double _calculateVariance(List<double> values) {
    if (values.length < 2) return 0.0;
    final mean = values.reduce((a, b) => a + b) / values.length;
    final squaredDiffs = values.map((x) => math.pow(x - mean, 2));
    return squaredDiffs.reduce((a, b) => a + b) / values.length;
  }

  static double _calculateInteractionIntensity(BehavioralData data) {
    final totalInteractions = data.keystrokeTimes.length + data.tapPositions.length;
    final sessionDuration = DateTime.now().millisecondsSinceEpoch - data.sessionStartTime;
    return sessionDuration > 0 ? totalInteractions / (sessionDuration / 1000) : 0.0;
  }

  static double _calculateSessionConsistency(BehavioralData data) {
    if (data.keystrokeTimes.length < 3) return 0.5;
    final intervals = _calculateIntervals(data.keystrokeTimes);
    final variance = _calculateVariance(intervals.map((e) => e.toDouble()).toList());
    return 1.0 / (1.0 + variance / 1000); // Lower variance = higher consistency
  }

  static double _assessDataQuality(Map<String, dynamic> features) {
    double quality = 0.0;
    
    // More keystrokes = better data quality
    final keystrokeCount = features['keystroke_count'] ?? 0;
    quality += (keystrokeCount / 20).clamp(0.0, 1.0) * 0.4;
    
    // Reasonable session duration
    final sessionDuration = features['session_duration'] ?? 0;
    if (sessionDuration > 10000 && sessionDuration < 300000) {
      quality += 0.3;
    }
    
    // Complete feature set
    final completeness = features.values.where((v) => v != null && v != 'unknown').length / features.length;
    quality += completeness * 0.3;
    
    return quality.clamp(0.0, 1.0);
  }

  static int _getTimeSinceLastLogin() {
    // Simulate time since last login (in production, would query from database)
    return DateTime.now().difference(DateTime.now().subtract(Duration(days: 1))).inMilliseconds;
  }

  static double _calculateLoginFrequencyScore() {
    // Simulate login frequency analysis (in production, would analyze historical data)
    return 0.7; // Regular user
  }

  /// Load user's behavioral baseline
  static Future<Map<String, dynamic>?> _loadUserBaseline(String userId) async {
    final baselineJson = _prefs!.getString('${_keyUserBaseline}_$userId');
    if (baselineJson != null) {
      return jsonDecode(baselineJson);
    }
    return null;
  }

  /// Update user baseline with new data
  static Future<void> _updateUserBaseline(String userId, Map<String, dynamic> features) async {
    try {
      final existing = await _loadUserBaseline(userId) ?? <String, dynamic>{};
      
      // Update keystroke patterns
      existing['keystroke_patterns'] = {
        'avg_typing_speed': _updateAverage(
          existing['keystroke_patterns']?['avg_typing_speed'], 
          features['typing_speed']
        ),
        'avg_variance': _updateAverage(
          existing['keystroke_patterns']?['avg_variance'], 
          features['keystroke_variance']
        ),
      };
      
      // Update timing patterns
      final typicalHours = (existing['timing_patterns']?['typical_hours'] as List? ?? []).cast<int>();
      final currentHour = features['login_hour'];
      if (!typicalHours.contains(currentHour)) {
        typicalHours.add(currentHour);
        if (typicalHours.length > 10) typicalHours.removeAt(0); // Keep recent hours
      }
      
      existing['timing_patterns'] = {
        'typical_hours': typicalHours,
        'typical_days': _updateTypicalDays(
          existing['timing_patterns']?['typical_days'],
          features['login_day_of_week']
        ),
      };
      
      // Update session patterns
      existing['session_patterns'] = {
        'avg_duration': _updateAverage(
          existing['session_patterns']?['avg_duration'],
          features['session_duration']
        ),
        'avg_intensity': _updateAverage(
          existing['session_patterns']?['avg_intensity'],
          features['interaction_intensity']
        ),
      };
      
      // Update device patterns
      final knownDevices = (existing['device_patterns']?['known_devices'] as List? ?? []).cast<String>();
      final currentDevice = features['device_info'];
      if (!knownDevices.contains(currentDevice)) {
        knownDevices.add(currentDevice);
        if (knownDevices.length > 5) knownDevices.removeAt(0); // Keep recent devices
      }
      
      existing['device_patterns'] = {
        'known_devices': knownDevices,
        'known_locations': _updateKnownLocations(
          existing['device_patterns']?['known_locations'],
          features['location_context']
        ),
      };
      
      // Update strength based on data points
      existing['strength'] = (existing['strength'] ?? 0.0) + 0.1;
      existing['strength'] = (existing['strength'] as double).clamp(0.0, 1.0);
      
      existing['last_updated'] = DateTime.now().millisecondsSinceEpoch;
      
      await _prefs!.setString('${_keyUserBaseline}_$userId', jsonEncode(existing));
    } catch (e) {
      print('Error updating baseline: $e');
    }
  }

  static double _updateAverage(dynamic existing, dynamic newValue) {
    if (existing == null || newValue == null) return newValue ?? 0.0;
    return (existing + newValue) / 2;
  }

  static List<int> _updateTypicalDays(dynamic existing, int newDay) {
    final days = (existing as List? ?? []).cast<int>();
    if (!days.contains(newDay)) {
      days.add(newDay);
    }
    return days;
  }

  static List<String> _updateKnownLocations(dynamic existing, String? newLocation) {
    final locations = (existing as List? ?? []).cast<String>();
    if (newLocation != null && !locations.contains(newLocation)) {
      locations.add(newLocation);
      if (locations.length > 3) locations.removeAt(0);
    }
    return locations;
  }
}

/// Result of ML-based login safety prediction
class LoginSafetyResult {
  final bool isSafe;
  final double safetyProbability;
  final double confidence;
  final SafetyLevel safetyLevel;
  final Map<String, double> anomalyScores;
  final List<String> recommendations;
  final List<String> riskFactors;
  final DateTime timestamp;

  LoginSafetyResult({
    required this.isSafe,
    required this.safetyProbability,
    required this.confidence,
    required this.safetyLevel,
    required this.anomalyScores,
    required this.recommendations,
    required this.riskFactors,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'is_safe': isSafe,
      'safety_probability': safetyProbability,
      'confidence': confidence,
      'safety_level': safetyLevel.toString(),
      'anomaly_scores': anomalyScores,
      'recommendations': recommendations,
      'risk_factors': riskFactors,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

enum SafetyLevel {
  critical,
  low,
  medium,
  high,
}
