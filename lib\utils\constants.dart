import 'package:flutter/material.dart';

class AppColors {
  static const Color gradientStart = Color(0xFF667eea);
  static const Color gradientMid = Color(0xFF764ba2);
  static const Color gradientEnd = Color(0xFFf093fb);

  static const Color buttonStart = Color(0xFF4facfe);
  static const Color buttonEnd = Color(0xFF00f2fe);

  static const Color white = Colors.white;
  static const Color white70 = Colors.white70;
  static const Color white80 = Color.fromRGBO(255, 255, 255, 0.8);
  static const Color white30 = Color.fromRGBO(255, 255, 255, 0.3);

  // Legacy colors for compatibility
  static const Color primary = Color(0xFF4285F4);
  static const Color background = Color(0xFF0D1B2A);
  static const Color surface = Color(0xFF1B263B);
  static const Color error = Color(0xFFEF4444);

  // Banking specific colors
  static const Color bankingPrimary = Color(0xFF4285F4);
  static const Color bankingSecondary = Color(0xFF34A853);
  static const Color bankingAccent = Color(0xFFFF6B35);
  static const Color bankingBackground = Color(0xFF0D1B2A);
  static const Color bankingSurface = Color(0xFF1B263B);
  static const Color bankingCard = Color(0xFF2A3441);

  // Transaction colors
  static const Color creditGreen = Color(0xFF10B981);
  static const Color debitRed = Color(0xFFEF4444);
  static const Color pendingOrange = Color(0xFFF59E0B);
}

class BankingConstants {
  static const String appName = 'Trust Chain Banking';
  static const String tagline = 'Secure Banking with Behavioral Authentication';
  static const String currency = '₹';

  // Dynamic user data - will be replaced by actual user data
  static String get defaultAccountNumber => _generateAccountNumber();
  static const String defaultUserName = 'Demo User';

  // Demo data - configurable for different scenarios
  static const double defaultBalance = 125400.00; // ₹1,25,400.00
  static String get defaultCardNumber => _generateCardNumber();
  static const String defaultBankName = 'Trust Chain Bank';

  // Banking features
  static String get ifscCode => 'TCBN${_generateBranchCode()}';
  static const String branchName = 'Mumbai Central';
  static const String accountType = 'Savings Account';

  // Helper methods for dynamic data generation
  static String _generateAccountNumber() {
    final now = DateTime.now();
    final suffix = (now.millisecondsSinceEpoch % 10000).toString().padLeft(4, '0');
    return '1234 5678 90$suffix';
  }

  static String _generateCardNumber() {
    final now = DateTime.now();
    final suffix = (now.millisecondsSinceEpoch % 10000).toString().padLeft(4, '0');
    return '1234 5678 90$suffix';
  }

  static String _generateBranchCode() {
    final now = DateTime.now();
    return (now.millisecondsSinceEpoch % 10000).toString().padLeft(4, '0');
  }
}

class AppSpacing {
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
}

class AppTextStyles {
  static const TextStyle title = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: Colors.white,
    letterSpacing: 1.5,
  );

  static const TextStyle subtitle = TextStyle(
    fontSize: 16,
    color: AppColors.white80,
    letterSpacing: 0.5,
  );

  static const TextStyle hint = TextStyle(
    color: AppColors.white70,
  );

  static const TextStyle input = TextStyle(
    color: AppColors.white,
  );

  static const TextStyle progress = TextStyle(
    fontSize: 14,
    color: AppColors.white80,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    color: Colors.white,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    color: Colors.white,
  );
}
