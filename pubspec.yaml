name: trust_chain_banking
description: "Trust Chain Banking - Secure Banking with Behavioral Authentication"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  path_provider: ^2.1.5
  shared_preferences: ^2.5.3
  firebase_core: ^2.27.0
  cloud_firestore: ^4.15.0
  device_info_plus: ^10.1.0
  fl_chart: ^0.68.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  # geolocator: ^11.0.0  # Temporarily disabled due to Android build issues
  permission_handler: ^11.3.1
  sensors_plus: ^4.0.2  # For accelerometer (panic gesture detection)
  intl: ^0.19.0  # For INR currency formatting
  shimmer: ^3.0.0  # For loading animations
  lottie: ^3.1.2  # For animated icons and glowing effects
  flutter_svg: ^2.0.10+1  # For SVG icons
  geolocator: ^11.0.0
  
  # 🚀 HACKATHON: Production-Grade ML & Security
  # tflite_flutter: ^0.9.0  # Temporarily disabled for demo compatibility
  crypto: ^3.0.3  # Enhanced cryptographic operations
  encrypt: ^5.0.3  # Additional encryption capabilities
  local_auth: ^2.1.8  # Biometric authentication integration
  flutter_secure_storage: ^9.2.2  # Secure data storage
  collection: ^1.18.0  # Enhanced data structures for ML
  ml_algo: ^16.10.0  # Machine learning algorithms
  ml_dataframe: ^1.4.1  # Data processing for ML
  
  # 🔗 ICP Integration Dependencies
  http: ^1.2.0  # HTTP client for ICP API calls
  convert: ^3.1.1  # Data conversion utilities
  
  # 🔒 Masumi Privacy Dependencies  
  pointycastle: ^3.7.4  # Advanced cryptography for differential privacy
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
