#!/bin/bash

# Trust Chain Banking - ICP Setup Script
echo "🚀 Setting up ICP Canister for Trust Chain Banking..."

# Check if dfx is installed
if ! command -v dfx &> /dev/null; then
    echo "❌ DFX not found. Installing DFX..."
    sh -ci "$(curl -fsSL https://internetcomputer.org/install.sh)"
    export PATH="$HOME/bin:$PATH"
fi

# Navigate to ICP config directory
cd icp_config

# Start local replica
echo "🔗 Starting local ICP replica..."
dfx start --background --clean

# Deploy the canister
echo "⛓️ Deploying behavioral authentication canister..."
dfx deploy trust_chain_behavioral_auth

# Get canister ID
CANISTER_ID=$(dfx canister id trust_chain_behavioral_auth)
echo "✅ Canister deployed successfully!"
echo "📋 Canister ID: $CANISTER_ID"

# Update the Flutter app with the new canister ID
echo "🔄 Updating Flutter app configuration..."
sed -i "s/rdmx6-jaaaa-aaaah-qdrqq-cai/$CANISTER_ID/g" ../lib/services/icp_integration_service.dart

echo "🎉 ICP setup complete!"
echo "   Canister ID: $CANISTER_ID"
echo "   Local replica: http://localhost:4943"
echo "   Ready for blockchain behavioral authentication!"
