import 'dart:math' as math;

/// Enhanced authentication result with detailed metrics
class AuthenticationResult {
  final bool isAuthenticated;
  final double score;
  final double confidence;
  final String riskLevel;
  final int featureCount;
  final int processingTime;

  AuthenticationResult({
    required this.isAuthenticated,
    required this.score,
    required this.confidence,
    required this.riskLevel,
    required this.featureCount,
    required this.processingTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'isAuthenticated': isAuthenticated,
      'score': score,
      'confidence': confidence,
      'riskLevel': riskLevel,
      'featureCount': featureCount,
      'processingTime': processingTime,
    };
  }
}

class BehavioralData {
  final double touchPressure;
  final double touchDuration;
  final double swipeVelocity;
  final double deviceTilt;
  final double typingRhythm;
  final DateTime timestamp;

  BehavioralData({
    required this.touchPressure,
    required this.touchDuration,
    required this.swipeVelocity,
    required this.deviceTilt,
    required this.typingRhythm,
    required this.timestamp,
  });

  // Convert to feature vector for ML processing
  List<double> toFeatureVector() {
    return [
      touchPressure,
      touchDuration,
      swipeVelocity,
      deviceTilt,
      typingRhythm,
    ];
  }

  Map<String, dynamic> toJson() {
    return {
      'touchPressure': touchPressure,
      'touchDuration': touchDuration,
      'swipeVelocity': swipeVelocity,
      'deviceTilt': deviceTilt,
      'typingRhythm': typingRhythm,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory BehavioralData.fromJson(Map<String, dynamic> json) {
    return BehavioralData(
      touchPressure: json['touchPressure'],
      touchDuration: json['touchDuration'],
      swipeVelocity: json['swipeVelocity'],
      deviceTilt: json['deviceTilt'],
      typingRhythm: json['typingRhythm'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}
