import 'dart:async';

import 'package:flutter/material.dart';
import 'package:sensors_plus/sensors_plus.dart';
import 'package:trust_chain_banking/firebase/firebase_service.dart';
import 'package:trust_chain_banking/models/behavioral_data_model.dart';
import 'package:trust_chain_banking/services/auth_service.dart';
import 'package:trust_chain_banking/services/gyro_tracker.dart';
import 'package:trust_chain_banking/services/hold_angle_tracker.dart';
import 'package:trust_chain_banking/services/ocsvm_service.dart';
import '../utils/constants.dart' as constants;
import '../utils/currency_formatter.dart';
import '../routes/app_routes.dart';
import '../services/transaction_service.dart';
import '../models/transaction_model.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> with WidgetsBindingObserver{
  final TransactionService _transactionService = TransactionService();
  double _currentBalance = 0.0;
  List<Transaction> _recentTransactions = [];
 String _userName = 'Unknown';
  final String _accountNumber = constants.BankingConstants.defaultAccountNumber;
  final String _bankName = constants.BankingConstants.defaultBankName;
  bool _isLoading = true;
  Offset? _swipeStart;
  DateTime? _swipeStartTime;
  List<double> _swipeVelocities = [];
  List<Map<String, double>> _tapPositions = [];
  Timer? _uploadTimer;
  Timer? _debugTimer;
  String? _sessionId;
  DateTime? _sessionStartTime;
  final HoldAngleTracker _holdAngleTracker = HoldAngleTracker();
  final OCSVMService _ocsvmService = OCSVMService();
  bool _hasUploadedBehavioralData = false;



  @override
  void initState() {
    super.initState();
    _loadDashboardData();
    
    // Enable and start gyro tracking properly
    enableGyroTracking(); // 🔁 Enable gyro tracking
    
    // Test sensor availability first
    _testSensorAvailability();
    
    GyroTracker().startPassive(); // Start passive gyro tracking
    
    // Start hold angle tracking
    _holdAngleTracker.start();
    
    _sessionStartTime = DateTime.now(); // ✅ Session begins
    WidgetsBinding.instance.addObserver(this);
    
    // Start debug timer to periodically check data collection
    _startDebugTimer();
     
    // Dynamically fetch current user from AuthService
    _userName = AuthService.currentUserName ?? constants.BankingConstants.defaultUserName;
    debugPrint('[Dashboard] ✅ Loaded user: $_userName');
    debugPrint('[Dashboard] ✅ Gyro tracking enabled and passive started');
    debugPrint('[Dashboard] ✅ Hold angle tracking started');
    debugPrint('[Dashboard] ✅ OCSVM service initialized');
  }

  void _testSensorAvailability() {
    print('[Dashboard] Testing sensor availability...');
    
    // Test gyroscope
    try {
      final gyroStream = gyroscopeEvents;
      final gyroSub = gyroStream.listen((event) {
        print('[Dashboard] ✅ Gyroscope working: ${event.x}, ${event.y}, ${event.z}');
      });
      
      // Cancel after a short test
      Future.delayed(const Duration(milliseconds: 500), () {
        gyroSub.cancel();
      });
    } catch (e) {
      print('[Dashboard] ❌ Gyroscope error: $e');
    }
    
    // Test accelerometer
    try {
      final accelStream = accelerometerEvents;
      final accelSub = accelStream.listen((event) {
        print('[Dashboard] ✅ Accelerometer working: ${event.x}, ${event.y}, ${event.z}');
      });
      
      // Cancel after a short test
      Future.delayed(const Duration(milliseconds: 500), () {
        accelSub.cancel();
      });
    } catch (e) {
      print('[Dashboard] ❌ Accelerometer error: $e');
    }
  }

  void _startDebugTimer() {
    _debugTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      print('[Dashboard Debug] Current behavioral data status:');
      print('  - Tap positions: ${_tapPositions.length}');
      print('  - Swipe velocities: ${_swipeVelocities.length}');
      print('  - Session duration: ${DateTime.now().difference(_sessionStartTime!).inSeconds}s');
      
      // Check OCSVM status
      final ocsvmStats = _ocsvmService.getModelStats();
      print('[Dashboard Debug] OCSVM Status:');
      print('  - Model trained: ${ocsvmStats['is_trained']}');
      print('  - Training buffer: ${ocsvmStats['training_buffer_size']}');
      print('  - Is training: ${ocsvmStats['is_training']}');
      if (ocsvmStats['is_trained'] == true) {
        print('  - Accuracy: ${(ocsvmStats['accuracy'] * 100).toStringAsFixed(1)}%');
        print('  - Support vectors: ${ocsvmStats['support_vectors']}');
      }
      
      // Check if tracking is actually enabled and working
      _verifyTrackingStatus();
      
      // Run a test upload every 30 seconds (every 2nd debug cycle)
      if (timer.tick % 2 == 0) {
        print('[Dashboard] Running behavioral data recording test...');
        _testBehavioralDataRecording();
      }
    });
  }

  void _verifyTrackingStatus() async {
    print('[Dashboard] Verifying tracking status...');
    
    // Force a gyro burst and see if we get data
    GyroTracker().startBurst();
    
    // Wait for some data collection
    await Future.delayed(const Duration(milliseconds: 700));
    
    // Check if we collected any data
    final tempGyroData = GyroTracker().consumeMagnitudes();
    final tempHoldData = _holdAngleTracker.consumeHoldAngles();
    
    if (tempGyroData.isNotEmpty) {
      print('[Dashboard] ✅ Gyro tracking working - collected ${tempGyroData.length} samples');
    } else {
      print('[Dashboard] ❌ Gyro tracking not working - no samples collected');
      // Try to restart gyro tracking
      print('[Dashboard] Attempting to restart gyro tracking...');
      enableGyroTracking();
      GyroTracker().startPassive();
    }
    
    if (tempHoldData.isNotEmpty) {
      print('[Dashboard] ✅ Hold angle tracking working - collected ${tempHoldData.length} samples');
    } else {
      print('[Dashboard] ❌ Hold angle tracking not working - no samples collected');
      // Try to restart hold angle tracking
      print('[Dashboard] Attempting to restart hold angle tracking...');
      _holdAngleTracker.stop();
      await Future.delayed(const Duration(milliseconds: 100));
      _holdAngleTracker.start();
    }
  }
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print('[Dashboard] App lifecycle state changed to: $state');
    
    if (state == AppLifecycleState.detached || state == AppLifecycleState.inactive) {
      print('[Dashboard] App going inactive/detached - recording behavioral data');
      _recordBehavioralData();
    } else if (state == AppLifecycleState.paused) {
      print('[Dashboard] App paused - stopping tracking temporarily');
      GyroTracker().stopBurst();
    } else if (state == AppLifecycleState.resumed) {
      print('[Dashboard] App resumed - restarting tracking');
      if (!_hasUploadedBehavioralData) {
        GyroTracker().startPassive();
      }
    }
  }


  void _handleTapDown(TapDownDetails details) {
    final position = details.globalPosition;
    _tapPositions.add({
      'x': position.dx,
      'y': position.dy,
    });
    
    // Trigger gyro burst tracking on tap
    GyroTracker().startBurst();
    print('📍 Dashboard tap position: x=${position.dx}, y=${position.dy}');
    
    // Force a hold angle measurement by manually triggering one sample
    Future.delayed(const Duration(milliseconds: 100), () {
      print('📐 Triggered additional hold angle measurement after tap');
    });
  } 

 double _calculateAverageSwipeVelocity() {
  if (_swipeVelocities.isEmpty) return 0.0;
  final total = _swipeVelocities.reduce((a, b) => a + b);
  return total / _swipeVelocities.length;
}

  Future<void> _recordBehavioralData() async {
    try {
      final avgVelocity = _calculateAverageSwipeVelocity();
      print('[Dashboard] avgSwipeVelocity: $avgVelocity');

      final sessionEndTime = DateTime.now();
      final sessionDuration = sessionEndTime.difference(_sessionStartTime!).inSeconds.toDouble();
      
      // Consume hold angles and gyro data
      final dashboardAngles = _holdAngleTracker.consumeHoldAngles();
      final gyroMagnitudes = GyroTracker().consumeMagnitudes();

      print('[Dashboard] Behavioral data summary:');
      print('  - Tap positions: ${_tapPositions.length}');
      print('  - Swipe velocities: ${_swipeVelocities.length}');
      print('  - Gyro magnitudes: ${gyroMagnitudes.length}');
      print('  - Hold angles: ${dashboardAngles.length}');
      print('  - Session duration: ${sessionDuration}s');

      final data = BehavioralData(
        tapPositions: _tapPositions.map((pos) => Offset(pos['x']!, pos['y']!)).toList(),
        swipeVelocities: _swipeVelocities,
        gyroMagnitudes: gyroMagnitudes,
        sessionDuration: sessionDuration,
        holdAngles: dashboardAngles,
      );
      
      // 🎯 OCSVM Integration: Add behavioral data for training/authentication
      try {
        if (_ocsvmService.isReady) {
          // Model is trained, perform authentication
          final ocsvmResult = await _ocsvmService.authenticate(data);
          print('[Dashboard] 🔐 OCSVM Authentication Result: $ocsvmResult');
          
          // You could use this result for additional security decisions
          if (!ocsvmResult.isAuthenticated && ocsvmResult.riskLevel == 'CRITICAL') {
            print('[Dashboard] ⚠️ CRITICAL: Behavioral anomaly detected!');
            // Could trigger additional security measures here
          }
        } else {
          // Model not trained yet, add data for training
          await _ocsvmService.addTrainingData(data);
          print('[Dashboard] 📚 Added behavioral data to OCSVM training buffer');
        }
      } catch (e) {
        print('[Dashboard] ❌ OCSVM error: $e');
      }
      
      final currentUser = AuthService.currentUserName ?? 'Unknown';
      await FirebaseService().uploadBehavioralData(currentUser, data);
      print('[Dashboard] ✅ Behavioral data uploaded for user: $currentUser');
      
    } catch (e) {
      print('[Dashboard] ❌ Error recording behavioral data: $e');
    }
  }

  // Method to manually trigger behavioral data recording for testing
  void _testBehavioralDataRecording() async {
    print('[Dashboard] Testing behavioral data recording...');
    
    // Add some test data if we don't have any
    if (_tapPositions.isEmpty) {
      _tapPositions.addAll([
        {'x': 100.0, 'y': 200.0},
        {'x': 150.0, 'y': 250.0},
      ]);
      print('[Dashboard] Added test tap positions');
    }
    
    if (_swipeVelocities.isEmpty) {
      _swipeVelocities.addAll([150.0, 200.0, 175.0]);
      print('[Dashboard] Added test swipe velocities');
    }
    
    // Force some gyro and hold angle data collection
    GyroTracker().startBurst();
    await Future.delayed(const Duration(milliseconds: 600));
    
    await _recordBehavioralData();
  }


  void _handleSwipeStart(DragStartDetails details) {
    _swipeStart = details.localPosition;
    _swipeStartTime = DateTime.now();
    
    // Trigger gyro burst tracking on swipe start
    GyroTracker().startBurst();
    print('🔥 Dashboard swipe started - gyro burst triggered');
  }

  void _handleSwipeEnd(DragEndDetails details) {
    if (_swipeStart == null || _swipeStartTime == null) return;

    final endTime = DateTime.now();
    final durationMs = endTime.difference(_swipeStartTime!).inMilliseconds;
    if (durationMs > 0) {
      final velocity = details.velocity.pixelsPerSecond.distance;
      final seconds = durationMs / 1000;
      final avgVelocity = velocity / seconds;
      _swipeVelocities.add(avgVelocity);
      print('🏃 Dashboard swipe velocity: $avgVelocity');
    }

    _swipeStart = null;
    _swipeStartTime = null;
    
    // Consume gyro data after swipe (no separate burst consumption needed)
    Future.delayed(const Duration(milliseconds: 300), () {
      print('📊 Dashboard gyro data collection completed');
    });
  } 



  Future<void> _loadDashboardData() async {
    try {
      await _transactionService.initialize();
      setState(() {
        _currentBalance = _transactionService.getCurrentBalance();
        _recentTransactions = _transactionService.getRecentTransactions(limit: 4);
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading dashboard data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
          onPanStart: _handleSwipeStart,
          onPanEnd: _handleSwipeEnd,
              behavior: HitTestBehavior.translucent,
             onTapDown: _handleTapDown,
      child: Scaffold(
        backgroundColor: constants.AppColors.bankingBackground,
        body: SafeArea(
          child: NotificationListener<ScrollNotification>(
            onNotification: (notification) {
              if (notification is ScrollUpdateNotification) {
                final double scrollDelta = notification.scrollDelta ?? 0.0;
                final Duration elapsed = const Duration(milliseconds: 16);
                final double velocity = scrollDelta / (elapsed.inMilliseconds / 1000);
                if (velocity.abs() > 0) {
                  _swipeVelocities.add(velocity.abs());
                  print('[Dashboard Scroll] Velocity recorded: $velocity');
                }
                
                // Trigger burst on scroll movement
                GyroTracker().startBurst();
              }
              return false;
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 24),
                  _buildBalanceCard(),
                  const SizedBox(height: 24),
                  _buildQuickActions(),
                  const SizedBox(height: 24),
                  _buildQuickSend(),
                  const SizedBox(height: 24),
                  _buildRecentTransactions(),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: _buildBottomNavigation(),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // User Avatar
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                constants.AppColors.bankingPrimary,
                constants.AppColors.bankingSecondary,
              ],
            ),
          ),
          child: const Icon(
            Icons.person,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),

        // Welcome Text
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome back',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
              Text(
                _userName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Service Status & Emergency Icons
        Flexible(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Service Status Row
              Flexible(child: _buildServiceStatusRow()),
              const SizedBox(width: 8),
              GestureDetector(
                onTap: () => _showEmergencyOptions(),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFFDC2626),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.emergency,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: constants.AppColors.bankingSurface,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.notifications_outlined,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildServiceStatusRow() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B).withOpacity(0.8),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // OCSVM ML Status
          _buildServiceIndicator(
            'ML',
            _getOCSVMStatusColor(),
            _getOCSVMStatusIcon(),
            () => _showServiceStatus('OCSVM Behavioral ML', _getOCSVMStatusDescription()),
          ),
          const SizedBox(width: 8),
          // ICP Blockchain Status (Demo Mode)
          _buildServiceIndicator(
            'ICP',
            Colors.amber,
            Icons.link,
            () => _showServiceStatus('ICP Blockchain', 'Demo Mode - Blockchain integration ready for deployment'),
          ),
          const SizedBox(width: 8),
          // Masumi Privacy Status
          _buildServiceIndicator(
            'PVT',
            Colors.green,
            Icons.privacy_tip,
            () => _showServiceStatus('Masumi Privacy', 'Active - Differential privacy protection enabled'),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceIndicator(String label, Color color, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: color.withOpacity(0.15),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 12),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getOCSVMStatusDescription() {
    final stats = _ocsvmService.getModelStats();
    if (_ocsvmService.isTraining) {
      return 'Training behavioral authentication model...';
    } else if (_ocsvmService.isReady) {
      return 'Active - ML model ready for authentication\nTraining samples: ${stats['training_buffer_size']}';
    } else {
      return 'Initializing behavioral authentication system...';
    }
  }

  void _showServiceStatus(String serviceName, String description) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E293B),
        title: Row(
          children: [
            Icon(
              serviceName.contains('OCSVM') ? Icons.psychology : 
              serviceName.contains('ICP') ? Icons.link : Icons.privacy_tip,
              color: serviceName.contains('OCSVM') ? _getOCSVMStatusColor() :
                     serviceName.contains('ICP') ? Colors.amber : Colors.green,
              size: 24,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                serviceName,
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
            ),
          ],
        ),
        content: Text(
          description,
          style: const TextStyle(color: Colors.white70, fontSize: 14),
        ),
        actions: [
          if (serviceName.contains('OCSVM') && _ocsvmService.isReady)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _ocsvmService.forceRetrain();
              },
              child: const Text('Retrain ML Model'),
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Color _getOCSVMStatusColor() {
    if (_ocsvmService.isTraining) return Colors.orange;
    if (_ocsvmService.isReady) return Colors.green;
    return Colors.grey;
  }

  IconData _getOCSVMStatusIcon() {
    if (_ocsvmService.isTraining) return Icons.psychology;
    if (_ocsvmService.isReady) return Icons.security;
    return Icons.pending;
  }

  void _showOCSVMStatus() {
    _showServiceStatus('OCSVM Behavioral ML', _getOCSVMStatusDescription());
  }

  Widget _buildBalanceCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            constants.AppColors.bankingPrimary,
            constants.AppColors.bankingPrimary.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: constants.AppColors.bankingPrimary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Your Balance',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                ),
              ),
              Text(
                _bankName,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Text(
            _currentBalance.inr,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Card Number
          Row(
            children: [
              const Icon(
                Icons.credit_card,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _accountNumber,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              // Mastercard logo placeholder
              Container(
                width: 40,
                height: 24,
                decoration: BoxDecoration(
                  color: constants.AppColors.bankingAccent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Center(
                  child: Text(
                    'MC',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(
              icon: Icons.send,
              label: 'Send',
              onTap: () {
                _showSendMoneyDialog();
              },
            ),
            _buildActionButton(
              icon: Icons.request_page,
              label: 'Request',
              onTap: () {
                _showRequestMoneyDialog();
              },
            ),
            _buildActionButton(
              icon: Icons.add_circle_outline,
              label: 'Top Up',
              onTap: () {
                _showTopUpDialog();
              },
            ),
          ],
        ),
        const SizedBox(height: 15),
        // 🚀 HACKATHON: ICP and Masumi Demo Button
        Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.icpMasumiDemo);
            },
            icon: const Icon(Icons.rocket_launch, color: Colors.white),
            label: const Text(
              '🚀 ICP & Masumi Integration Demo',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3B82F6),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        GyroTracker().startBurst(); // Trigger burst on tap
        onTap(); // Existing callback
        },
      child: Container(
        width: 80,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: constants.AppColors.bankingSurface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: constants.AppColors.bankingPrimary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: constants.AppColors.bankingPrimary,
                size: 20,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickSend() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Quick Send',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to all contacts
              },
              child: Text(
                'See all',
                style: TextStyle(
                  color: constants.AppColors.bankingPrimary,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        SizedBox(
          height: 80,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              _buildContactAvatar('Add', Icons.add, isAddButton: true),
              _buildContactAvatar('Priya', Icons.person),
              _buildContactAvatar('Rahul', Icons.person),
              _buildContactAvatar('Anita', Icons.person),
              _buildContactAvatar('Vikram', Icons.person),
              _buildContactAvatar('Meera', Icons.person),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContactAvatar(String name, IconData icon, {bool isAddButton = false}) {
    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: GestureDetector(
        onTap: () {
          if (isAddButton) {
            // Navigate to add contact
          } else {
            // Navigate to send money to contact
          }
        },
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isAddButton
                    ? constants.AppColors.bankingSurface
                    : constants.AppColors.bankingPrimary.withOpacity(0.2),
                shape: BoxShape.circle,
                border: isAddButton
                    ? Border.all(
                        color: constants.AppColors.bankingPrimary.withOpacity(0.3),
                        style: BorderStyle.solid,
                      )
                    : null,
              ),
              child: Icon(
                icon,
                color: isAddButton
                    ? constants.AppColors.bankingPrimary
                    : constants.AppColors.bankingPrimary,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              name,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Recent Transaction',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, AppRoutes.activity);
              },
              child: Text(
                'See all',
                style: TextStyle(
                  color: constants.AppColors.bankingPrimary,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Dynamic transaction list
        if (_recentTransactions.isEmpty)
          const Center(
            child: Text(
              'No recent transactions',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
          )
        else
          ...(_recentTransactions.map((transaction) => Column(
            children: [
              _buildTransactionItem(
                title: transaction.title,
                subtitle: transaction.description,
                amount: transaction.amount,
                isCredit: transaction.isCredit,
                icon: transaction.icon,
                date: transaction.formattedDate,
              ),
              if (transaction != _recentTransactions.last)
                const SizedBox(height: 12),
            ],
          )).toList()),
      ],
    );
  }

  Widget _buildTransactionItem({
    required String title,
    required String subtitle,
    required double amount,
    required bool isCredit,
    required IconData icon,
    String? date,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isCredit
                  ? constants.AppColors.creditGreen.withOpacity(0.2)
                  : constants.AppColors.debitRed.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: isCredit
                  ? constants.AppColors.creditGreen
                  : constants.AppColors.debitRed,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 14,
                  ),
                ),
                if (date != null)
                  Text(
                    date!,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.4),
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),

          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${isCredit ? '+' : '-'}${amount.inr}',
                style: TextStyle(
                  color: isCredit
                      ? constants.AppColors.creditGreen
                      : constants.AppColors.debitRed,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavItem(Icons.home, 'Home', true),
          _buildNavItem(Icons.bar_chart, 'Activity', false, onTap: () {
            Navigator.pushNamed(context, AppRoutes.activity);
          }),
          _buildNavItem(Icons.favorite_border, 'Favorites', false, onTap: () {
            _showFavoritesBottomSheet();
          }),
          _buildNavItem(Icons.person_outline, 'Profile', false, onTap: () {
            Navigator.pushNamed(context, AppRoutes.profile);
          }),
        ],
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, bool isActive, {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: () {
        GyroTracker().startBurst(); // Burst on nav icon tap
        if (onTap != null) onTap();
        },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: isActive
                ? constants.AppColors.bankingPrimary
                : Colors.white.withOpacity(0.5),
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: isActive
                  ? constants.AppColors.bankingPrimary
                  : Colors.white.withOpacity(0.5),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _showSendMoneyDialog() {
    GyroTracker().startBurst(); // Trigger burst sample
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.send, color: Color(0xFF3B82F6)),
              SizedBox(width: 8),
              Text(
                'Send Money',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Send Money feature will be available soon. You can transfer money to any UPI ID, bank account, or mobile number.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF3B82F6)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showRequestMoneyDialog() {
    GyroTracker().startBurst();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.request_page, color: Color(0xFF10B981)),
              SizedBox(width: 8),
              Text(
                'Request Money',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Request Money feature will be available soon. You can request money from contacts via SMS, WhatsApp, or email.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF10B981)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showTopUpDialog() {
    GyroTracker().startBurst();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.add_circle_outline, color: Color(0xFFFBBF24)),
              SizedBox(width: 8),
              Text(
                'Add Money',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Add Money feature will be available soon. You can add money from your linked bank accounts, debit cards, or UPI.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFFFBBF24)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showEmergencyOptions() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.emergency, color: Color(0xFFDC2626)),
              SizedBox(width: 8),
              Text(
                'Emergency Options',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildEmergencyOption(
                icon: Icons.block,
                title: 'Block Account',
                subtitle: 'Temporarily block all transactions',
                color: const Color(0xFFDC2626),
                onTap: () => _blockAccount(),
              ),
              const SizedBox(height: 12),
              _buildEmergencyOption(
                icon: Icons.phone,
                title: 'Emergency Contact',
                subtitle: 'Call bank emergency helpline',
                color: const Color(0xFFFBBF24),
                onTap: () => _callEmergencyHelpline(),
              ),
              const SizedBox(height: 12),
              _buildEmergencyOption(
                icon: Icons.security,
                title: 'Security Alert',
                subtitle: 'Report suspicious activity',
                color: const Color(0xFF3B82F6),
                onTap: () => _reportSuspiciousActivity(),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmergencyOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _blockAccount() {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: const Row(
            children: [
              Icon(Icons.warning, color: Color(0xFFDC2626)),
              SizedBox(width: 8),
              Text(
                'Block Account',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'Are you sure you want to block your account? This will prevent all transactions until you contact the bank.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _confirmAccountBlock();
              },
              child: const Text(
                'Block Account',
                style: TextStyle(color: Color(0xFFDC2626)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _confirmAccountBlock() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Account blocked successfully. Contact bank to unblock.'),
        backgroundColor: Color(0xFFDC2626),
      ),
    );
  }

  void _callEmergencyHelpline() {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Emergency Helpline: 1800-123-4567'),
        backgroundColor: Color(0xFFFBBF24),
      ),
    );
  }

  void _reportSuspiciousActivity() {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Security alert sent. Bank will contact you shortly.'),
        backgroundColor: Color(0xFF3B82F6),
      ),
    );
  }

  // Show favorites bottom sheet
  void _showFavoritesBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(height: 12),
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Favorite Transactions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                children: [
                  _buildFavoriteItem(
                    'UPI to Priya Sharma',
                    'Rent Payment',
                    Icons.home,
                    '₹25,000',
                  ),
                  _buildFavoriteItem(
                    'Electricity Bill',
                    'MSEB Payment',
                    Icons.flash_on,
                    '₹3,200',
                  ),
                  _buildFavoriteItem(
                    'Amazon Purchase',
                    'Quick Shopping',
                    Icons.shopping_bag,
                    'Variable',
                  ),
                  _buildFavoriteItem(
                    'Petrol - Shell',
                    'Vehicle Fuel',
                    Icons.local_gas_station,
                    '₹2,000',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteItem(String title, String subtitle, IconData icon, String amount) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: constants.AppColors.bankingPrimary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: constants.AppColors.bankingPrimary,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

@override
void dispose() {
  WidgetsBinding.instance.removeObserver(this);

  // Stop debug timer
  _debugTimer?.cancel();
  
  // Stop tracking
  _holdAngleTracker.stop();
  GyroTracker().stopBurst();
  GyroTracker().stopPassive();  

  if (!_hasUploadedBehavioralData) {
    _recordBehavioralData();
    _hasUploadedBehavioralData = true;
  }

  super.dispose();
}

}