# Firebase Integration Guide for Trust Chain Banking

## Current Status
- ✅ Firebase Core initialized
- ✅ Cloud Firestore for data storage
- ✅ Behavioral data upload
- ❌ Firebase Authentication not implemented

## To Enable Firebase Authentication

### 1. Update pubspec.yaml
```yaml
dependencies:
  firebase_auth: ^4.15.0  # Add this line
  google_sign_in: ^6.1.0  # Optional: for Google login
```

### 2. Create Firebase Auth Service
Create `lib/services/firebase_auth_service.dart`:

```dart
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';

class FirebaseAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  // Current user
  User? get currentUser => _auth.currentUser;
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Email & Password Registration
  Future<UserCredential?> registerWithEmailPassword(String email, String password) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      return credential;
    } catch (e) {
      print('Registration error: $e');
      return null;
    }
  }

  // Email & Password Login
  Future<UserCredential?> signInWithEmailPassword(String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return credential;
    } catch (e) {
      print('Login error: $e');
      return null;
    }
  }

  // Google Sign In
  Future<UserCredential?> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) return null;

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      return await _auth.signInWithCredential(credential);
    } catch (e) {
      print('Google sign in error: $e');
      return null;
    }
  }

  // Sign Out
  Future<void> signOut() async {
    await _googleSignIn.signOut();
    await _auth.signOut();
  }

  // Send Password Reset Email
  Future<bool> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return true;
    } catch (e) {
      print('Password reset error: $e');
      return false;
    }
  }
}
```

### 3. Update AuthService to use Firebase
Modify `lib/services/auth_service.dart`:

```dart
import 'package:firebase_auth/firebase_auth.dart';
import 'firebase_auth_service.dart';

class AuthService {
  static final FirebaseAuthService _firebaseAuth = FirebaseAuthService();
  
  static Future<bool> login(String email, String password) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailPassword(email, password);
      if (credential != null) {
        // Store additional data locally if needed
        await _prefs!.setString(_keyUserToken, credential.user!.uid);
        await _prefs!.setInt(_keyLastLoginTime, DateTime.now().millisecondsSinceEpoch);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> register(Map<String, dynamic> userData) async {
    try {
      final credential = await _firebaseAuth.registerWithEmailPassword(
        userData['email'],
        userData['password'],
      );
      
      if (credential != null) {
        // Store user profile in Firestore
        await FirebaseFirestore.instance
            .collection('users')
            .doc(credential.user!.uid)
            .set({
          'name': userData['name'],
          'email': userData['email'],
          'phone': userData['phone'],
          'createdAt': FieldValue.serverTimestamp(),
          'behavioralProfile': {},
        });
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<void> logout() async {
    await _firebaseAuth.signOut();
    await _prefs!.remove(_keyUserToken);
    await _prefs!.remove(_keyLastLoginTime);
  }
}
```

### 4. Update Login Screens
Modify login screens to use Firebase Auth:

```dart
// In SecureLoginScreen
Future<void> _handleLogin() async {
  if (!_formKey.currentState!.validate()) return;
  
  setState(() => _isLoading = true);
  
  try {
    final success = await AuthService.login(
      _emailController.text.trim(),
      _passwordController.text,
    );
    
    if (success) {
      // Record behavioral data with Firebase user ID
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        await FirebaseService().uploadBehavioralData(
          user.uid,
          _currentBehavioralData,
        );
      }
      
      Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
    } else {
      _showError('Invalid credentials');
    }
  } catch (e) {
    _showError('Login failed: $e');
  } finally {
    setState(() => _isLoading = false);
  }
}
```

### 5. Add Firebase Auth Rules
In Firebase Console > Firestore > Rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow access to behavioral data subcollections
      match /sessions/{sessionId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /transactions/{transactionId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Emergency alerts (write-only for security)
    match /emergency_alerts/{alertId} {
      allow write: if request.auth != null;
    }
  }
}
```

### 6. Benefits of Firebase Auth Integration

**Security Advantages:**
- ✅ Server-side authentication validation
- ✅ Automatic token refresh
- ✅ Multi-factor authentication support
- ✅ Security rule enforcement
- ✅ Password reset functionality
- ✅ Account verification

**Behavioral Auth Enhancement:**
- ✅ User ID consistency across sessions
- ✅ Cloud-based behavioral profile storage
- ✅ Cross-device behavioral tracking
- ✅ Real-time fraud detection
- ✅ Centralized security monitoring

**Integration Steps:**
1. Run `flutter pub get` after adding dependencies
2. Update Firebase Console authentication settings
3. Test registration and login flows
4. Migrate existing local user data to Firebase
5. Update all screens to use Firebase user IDs

## Current Working Features (Local Auth)
- ✅ User registration and login
- ✅ Session management with tokens
- ✅ Behavioral data collection
- ✅ Biometric authentication
- ✅ ML-based security analysis
- ✅ Data upload to Firestore

## Migration Strategy
1. **Phase 1**: Add Firebase Auth alongside existing system
2. **Phase 2**: Test dual authentication systems
3. **Phase 3**: Migrate existing users to Firebase
4. **Phase 4**: Remove local authentication system
5. **Phase 5**: Full Firebase integration complete

Your app already has a robust authentication system working locally with advanced behavioral analytics. Firebase integration would add cloud-based security and cross-device capabilities.
