import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class AndroidSecurity {
  static const MethodChannel _channel = MethodChannel('android_security');

  /// Prevent screenshots and screen recording on Android
  static Future<void> enableScreenSecurity() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        await _channel.invokeMethod('enableScreenSecurity');
      } catch (e) {
        // Failed to enable screen security - continue
      }
    }
  }

  /// Disable screenshot prevention (for development)
  static Future<void> disableScreenSecurity() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        await _channel.invokeMethod('disableScreenSecurity');
      } catch (e) {
        // Failed to disable screen security - continue
      }
    }
  }

  /// Check if device is rooted (Android specific)
  static Future<bool> isDeviceRooted() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        return await _channel.invokeMethod('isDeviceRooted') ?? false;
      } catch (e) {
        // Failed to check root status - assume not rooted
        return false;
      }
    }
    return false;
  }

  /// Check if running on emulator (Android specific)
  static Future<bool> isEmulator() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        return await _channel.invokeMethod('isEmulator') ?? false;
      } catch (e) {
        // Failed to check emulator status - assume not emulator
        return false;
      }
    }
    return false;
  }

  /// Check for Xposed framework (Android specific)
  static Future<bool> hasXposedFramework() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        return await _channel.invokeMethod('hasXposedFramework') ?? false;
      } catch (e) {
        // Failed to check Xposed framework - assume not present
        return false;
      }
    }
    return false;
  }

  /// Check if developer mode is enabled (Android specific)
  static Future<bool> isDeveloperModeEnabled() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        return await _channel.invokeMethod('isDeveloperModeEnabled') ?? false;
      } catch (e) {
        // Failed to check developer mode - assume disabled
        return false;
      }
    }
    return false;
  }
}
