import 'package:flutter/material.dart';
import '../models/behavioral_data_model.dart';
import '../services/data_exporter.dart';


class BehavioralDataScreen extends StatelessWidget {
  final BehavioralData? data;

  const BehavioralDataScreen({this.data, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final json = data?.toJson() ?? {};

    return Scaffold(
      appBar: AppBar(
        title: Text('Behavioral Data'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      backgroundColor: const Color(0xFF667eea),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF667eea), Color(0xFF764ba2)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(20),
          children: [
            _buildCard('Keystroke Intervals', json['keystroke_intervals'].toString()),
            _buildCard('Avg Tap Pressure', json['avg_tap_pressure'].toStringAsFixed(2)),
            _buildCard('Tap Positions', json['tap_positions'].toString()),
            _buildCard('Avg Swipe Velocity', json['avg_swipe_velocity'].toStringAsFixed(2)),
            _buildCard('Session Duration (ms)', json['session_duration'].toString()),

            ElevatedButton(
  onPressed: () async {
    final exporter = DataExporter();
    final jsonData = data?.toJson() ?? {};

    await exporter.saveJsonToFile(jsonData, 'behavior_data');
    await exporter.saveCsvToFile(jsonData, 'behavior_data');

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Behavioral data exported to JSON and CSV!')),
    );
  },
  style: ElevatedButton.styleFrom(
    backgroundColor: Colors.white.withOpacity(0.2),
    foregroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
  ),
  child: Text('Export Behavioral Data'),
),

          ],
        ),
      ),
    );
  }

  Widget _buildCard(String title, String content) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              )),
          SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(color: Colors.white70),
          ),
        ],
      ),
    );
  }
}
