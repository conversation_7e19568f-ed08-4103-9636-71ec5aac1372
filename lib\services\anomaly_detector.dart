import 'dart:math' as math;

import '../models/behavioral_data_model.dart';

class AnomalyDetector {
  double calculateBehavioralScore(BehavioralData data) {
    double score = 0.0;

    // Keystroke timing consistency
    if (data.keystrokeTimes.length > 5) {
      score += 0.3;
    }


    // Reasonable session duration
    int sessionDuration = DateTime.now().millisecondsSinceEpoch - data.sessionStartTime;
    if (sessionDuration > 10000 && sessionDuration < 120000) {
      score += 0.4;
    }

    return math.min(1.0, score);
  }

  double _calculateVariance(List<double> values) {
    if (values.isEmpty) return 0.0;
    double mean = values.reduce((a, b) => a + b) / values.length;
    double variance = values
        .map((x) => math.pow(x - mean, 2))
        .reduce((a, b) => a + b) /
        values.length;
    return variance;
  }
}
