import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:sensors_plus/sensors_plus.dart';
import '../models/behavioral_data_model.dart';
import '../services/local_auth_service.dart';
import '../services/data_collector.dart';

class BackgroundAnomalyService {
  static final BackgroundAnomalyService _instance = BackgroundAnomalyService._internal();
  factory BackgroundAnomalyService() => _instance;
  BackgroundAnomalyService._internal();

  final LocalAuthService _localAuth = LocalAuthService();
  final DataCollector _dataCollector = DataCollector();
  
  Timer? _anomalyCheckTimer;
  Timer? _behavioralSamplingTimer;
  StreamSubscription? _accelerometerSubscription;
  
  bool _isRunning = false;
  BehavioralData? _currentSession;
  double _lastAnomalyScore = 1.0;
  
  // Behavioral patterns
  List<double> _touchPressures = [];
  List<int> _keystrokeTimes = [];
  List<double> _scrollVelocities = [];
  List<AccelerometerEvent> _accelerometerReadings = [];
  
  // Anomaly thresholds
  static const double _anomalyThreshold = 0.6;
  static const int _samplingIntervalSeconds = 30;
  static const int _anomalyCheckIntervalSeconds = 60;
  
  // Getters for monitoring
  bool get isRunning => _isRunning;
  double get lastAnomalyScore => _lastAnomalyScore;
  BehavioralData? get currentSession => _currentSession;

  /// Start background anomaly detection
  Future<void> startBackgroundDetection() async {
    if (_isRunning) return;

    _isRunning = true;
    
    // Initialize new behavioral session
    _currentSession = BehavioralData(
      sessionId: 'bg_session_${DateTime.now().millisecondsSinceEpoch}',
      keystrokeIntervals: [],
      touchPressures: [],
      scrollVelocities: [],
    );
    _currentSession!.sessionStartTime = DateTime.now().millisecondsSinceEpoch;
    
    // Start behavioral data sampling
    _startBehavioralSampling();
    
    // Start periodic anomaly checks
    _startAnomalyChecking();
    
    // Start sensor monitoring
    _startSensorMonitoring();
  }

  /// Stop background anomaly detection
  void stopBackgroundDetection() {
    if (!_isRunning) return;
    
    print('🔍 Stopping background anomaly detection...');
    _isRunning = false;
    
    _anomalyCheckTimer?.cancel();
    _behavioralSamplingTimer?.cancel();
    _accelerometerSubscription?.cancel();
    
    _anomalyCheckTimer = null;
    _behavioralSamplingTimer = null;
    _accelerometerSubscription = null;
  }

  /// Record user interaction for behavioral analysis
  void recordInteraction({
    double? touchPressure,
    int? keystrokeTime,
    double? scrollVelocity,
  }) {
    if (!_isRunning) return;
    
    if (touchPressure != null) {
      _touchPressures.add(touchPressure);
      if (_touchPressures.length > 50) {
        _touchPressures.removeAt(0); // Keep only recent data
      }
    }
    
    if (keystrokeTime != null) {
      _keystrokeTimes.add(keystrokeTime);
      if (_keystrokeTimes.length > 50) {
        _keystrokeTimes.removeAt(0);
      }
    }
    
    if (scrollVelocity != null) {
      _scrollVelocities.add(scrollVelocity);
      if (_scrollVelocities.length > 50) {
        _scrollVelocities.removeAt(0);
      }
    }
  }

  /// Get real-time anomaly assessment
  Future<Map<String, dynamic>> getAnomalyAssessment() async {
    if (!_isRunning || _currentSession == null) {
      return {
        'score': 1.0,
        'status': 'not_running',
        'anomalies': <String>[],
        'confidence': 0.0,
      };
    }
    
    final anomalies = <String>[];
    double totalScore = 1.0;
    
    // Check interaction patterns
    if (_touchPressures.isNotEmpty) {
      final pressureVariance = _calculateVariance(_touchPressures);
      if (pressureVariance > 0.5) {
        anomalies.add('Unusual touch pressure patterns');
        totalScore -= 0.2;
      }
    }
    
    // Check typing patterns
    if (_keystrokeTimes.length > 10) {
      final typingVariance = _calculateVariance(_keystrokeTimes.map((e) => e.toDouble()).toList());
      if (typingVariance > 10000) { // ms variance
        anomalies.add('Irregular typing rhythm');
        totalScore -= 0.15;
      }
    }
    
    // Check scroll behavior
    if (_scrollVelocities.isNotEmpty) {
      final avgScrollVelocity = _scrollVelocities.reduce((a, b) => a + b) / _scrollVelocities.length;
      if (avgScrollVelocity > 1000 || avgScrollVelocity < 10) {
        anomalies.add('Unusual scroll behavior');
        totalScore -= 0.1;
      }
    }
    
    // Check device orientation changes
    if (_accelerometerReadings.length > 20) {
      final startIndex = math.max(0, _accelerometerReadings.length - 20);
      final recentReadings = _accelerometerReadings.sublist(startIndex);
      final orientationChanges = _countOrientationChanges(recentReadings);
      if (orientationChanges > 10) {
        anomalies.add('Excessive device movement');
        totalScore -= 0.15;
      }
    }
    
    // Session duration check
    final sessionDuration = DateTime.now().millisecondsSinceEpoch - _currentSession!.sessionStartTime;
    if (sessionDuration < 5000) { // Less than 5 seconds
      anomalies.add('Very short session duration');
      totalScore -= 0.1;
    } else if (sessionDuration > 3600000) { // More than 1 hour
      anomalies.add('Unusually long session');
      totalScore -= 0.05;
    }
    
    // Time-based anomalies
    final currentHour = DateTime.now().hour;
    if (currentHour < 6 || currentHour > 23) {
      anomalies.add('Unusual usage time');
      totalScore -= 0.1;
    }
    
    totalScore = math.max(0.0, math.min(1.0, totalScore));
    _lastAnomalyScore = totalScore;
    
    return {
      'score': totalScore,
      'status': totalScore >= _anomalyThreshold ? 'normal' : 'anomaly_detected',
      'anomalies': anomalies,
      'confidence': _calculateConfidence(),
      'session_duration_minutes': (sessionDuration / (1000 * 60)).round(),
    };
  }

  /// Force an immediate anomaly check
  Future<double> forceAnomalyCheck() async {
    if (!_isRunning) return 1.0;
    
    final assessment = await getAnomalyAssessment();
    final score = assessment['score'] as double;
    
      // Update current session with latest data
      if (_currentSession != null) {
        _currentSession = BehavioralData(
          sessionId: _currentSession!.sessionId,
          keystrokeIntervals: _keystrokeTimes,
          touchPressures: _touchPressures,
          scrollVelocities: _scrollVelocities,
        );
        _currentSession!.sessionStartTime = _currentSession!.sessionStartTime;
        
        // Run detection through local auth service
        await _localAuth.runAnomalyDetection(_currentSession!);
      }    print('🔍 Anomaly Check - Score: $score, Status: ${assessment['status']}');
    
    return score;
  }

  // Private helper methods

  void _startBehavioralSampling() {
    // Reduced frequency for prototype - every 30 seconds instead of 5
    _behavioralSamplingTimer = Timer.periodic(
      const Duration(seconds: 30),
      (timer) async {
        if (!_isRunning) {
          timer.cancel();
          return;
        }

        // Simulate some behavioral data collection
        _simulateBehavioralData();
      },
    );
  }

  void _startAnomalyChecking() {
    // Reduced frequency for prototype - every 60 seconds instead of 10
    _anomalyCheckTimer = Timer.periodic(
      const Duration(seconds: 60),
      (timer) async {
        if (!_isRunning) {
          timer.cancel();
          return;
        }

        await forceAnomalyCheck();
      },
    );
  }

  void _startSensorMonitoring() {
    _accelerometerSubscription = accelerometerEventStream().listen(
      (AccelerometerEvent event) {
        if (!_isRunning) return;
        
        _accelerometerReadings.add(event);
        if (_accelerometerReadings.length > 100) {
          _accelerometerReadings.removeAt(0);
        }
      },
      onError: (error) {
        print('Accelerometer error: $error');
      },
    );
  }

  void _simulateBehavioralData() {
    // Simulate normal user interactions with some variance
    final random = math.Random();
    
    // Simulate touch pressure
    final basePressure = 0.5 + random.nextDouble() * 0.3;
    recordInteraction(touchPressure: basePressure);
    
    // Simulate keystroke timing
    final baseKeystroke = 150 + random.nextInt(100);
    recordInteraction(keystrokeTime: baseKeystroke);
    
    // Simulate scroll velocity
    final baseScroll = 200 + random.nextDouble() * 300;
    recordInteraction(scrollVelocity: baseScroll);
  }

  double _calculateVariance(List<double> values) {
    if (values.isEmpty) return 0.0;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance = values
        .map((x) => math.pow(x - mean, 2))
        .reduce((a, b) => a + b) / values.length;
    
    return variance;
  }

  int _countOrientationChanges(List<AccelerometerEvent> events) {
    if (events.length < 2) return 0;
    
    int changes = 0;
    for (int i = 1; i < events.length; i++) {
      final prev = events[i - 1];
      final curr = events[i];
      
      // Simple orientation change detection based on dominant axis
      final prevDominant = _getDominantAxis(prev);
      final currDominant = _getDominantAxis(curr);
      
      if (prevDominant != currDominant) {
        changes++;
      }
    }
    
    return changes;
  }

  String _getDominantAxis(AccelerometerEvent event) {
    final absX = event.x.abs();
    final absY = event.y.abs();
    final absZ = event.z.abs();
    
    if (absX > absY && absX > absZ) return 'x';
    if (absY > absZ) return 'y';
    return 'z';
  }

  double _calculateConfidence() {
    // Calculate confidence based on available data
    double confidence = 0.0;
    
    if (_touchPressures.length > 10) confidence += 0.25;
    if (_keystrokeTimes.length > 10) confidence += 0.25;
    if (_scrollVelocities.length > 10) confidence += 0.25;
    if (_accelerometerReadings.length > 20) confidence += 0.25;
    
    return confidence;
  }

  /// Get current detection statistics
  Map<String, dynamic> getDetectionStats() {
    return {
      'is_running': _isRunning,
      'last_anomaly_score': _lastAnomalyScore,
      'session_id': _currentSession?.sessionId,
      'data_points': {
        'touch_pressures': _touchPressures.length,
        'keystroke_times': _keystrokeTimes.length,
        'scroll_velocities': _scrollVelocities.length,
        'accelerometer_readings': _accelerometerReadings.length,
      },
      'session_duration_minutes': _currentSession != null 
        ? ((DateTime.now().millisecondsSinceEpoch - _currentSession!.sessionStartTime) / (1000 * 60)).round()
        : 0,
    };
  }
}
