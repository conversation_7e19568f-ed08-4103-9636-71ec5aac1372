import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';

/// 🚀 PERFORMANCE OPTIMIZER
/// Ensures smooth demo performance with optimized initialization and response times
class PerformanceOptimizer {
  static PerformanceOptimizer? _instance;
  static PerformanceOptimizer get instance => _instance ??= PerformanceOptimizer._();
  
  PerformanceOptimizer._();

  // Performance tracking
  final Map<String, List<int>> _operationTimes = {};
  final Map<String, dynamic> _performanceMetrics = {};
  final Map<String, Timer> _preloadTimers = {};
  
  // Optimization settings
  bool _isOptimized = false;
  bool _preloadingEnabled = true;
  bool _cachingEnabled = true;
  
  // Cache for frequently used data
  final Map<String, dynamic> _dataCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// Initialize performance optimization
  Future<void> initialize() async {
    if (_isOptimized) return;

    try {
      if (kDebugMode) {
        print('🚀 INITIALIZING PERFORMANCE OPTIMIZER - Preparing for Smooth Demo...');
      }

      // STEP 1: Setup performance monitoring
      _setupPerformanceMonitoring();

      // STEP 2: Initialize caching system
      _initializeCaching();

      // STEP 3: Start preloading critical data
      await _startPreloading();

      // STEP 4: Optimize memory usage
      _optimizeMemoryUsage();

      // STEP 5: Setup background optimization
      _setupBackgroundOptimization();

      _isOptimized = true;

      if (kDebugMode) {
        print('✅ PERFORMANCE OPTIMIZER READY - Demo will be lightning fast!');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Performance optimization failed: $e');
      }
    }
  }

  /// Setup performance monitoring
  void _setupPerformanceMonitoring() {
    _performanceMetrics.addAll({
      'total_operations': 0,
      'average_response_time': 0.0,
      'cache_hit_rate': 0.0,
      'memory_usage_mb': 0.0,
      'optimization_start_time': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// Initialize caching system
  void _initializeCaching() {
    if (kDebugMode) print('💾 Initializing intelligent caching system...');
    
    // Pre-populate cache with demo data
    _dataCache.addAll({
      'demo_user_profile': {
        'user_id': 'demo_user',
        'trust_score': 0.96,
        'behavioral_baseline': {
          'typing_speed': 45.0,
          'keystroke_rhythm': 0.85,
          'session_patterns': ['morning_active', 'evening_moderate'],
        },
      },
      'ml_model_cache': {
        'ensemble_accuracy': 0.96,
        'last_training': DateTime.now().subtract(const Duration(hours: 2)).millisecondsSinceEpoch,
        'model_version': '2.1.0',
      },
      'blockchain_cache': {
        'last_block_height': 12345,
        'network_status': 'healthy',
        'transaction_pool_size': 42,
      },
      'privacy_cache': {
        'differential_privacy_budget': 0.8,
        'k_anonymity_level': 5,
        'compliance_score': 95,
      },
    });

    // Set cache timestamps
    final now = DateTime.now();
    _dataCache.keys.forEach((key) {
      _cacheTimestamps[key] = now;
    });
  }

  /// Start preloading critical data
  Future<void> _startPreloading() async {
    if (!_preloadingEnabled) return;

    if (kDebugMode) print('⚡ Starting intelligent preloading...');

    // Preload in background to avoid blocking UI
    _preloadCriticalData();
    _preloadMLModels();
    _preloadNetworkConnections();
  }

  /// Preload critical data in background
  void _preloadCriticalData() {
    _preloadTimers['critical_data'] = Timer.periodic(
      const Duration(seconds: 2),
      (timer) => _refreshCriticalData(),
    );
  }

  /// Preload ML models
  void _preloadMLModels() {
    _preloadTimers['ml_models'] = Timer.periodic(
      const Duration(seconds: 5),
      (timer) => _warmupMLModels(),
    );
  }

  /// Preload network connections
  void _preloadNetworkConnections() {
    _preloadTimers['network'] = Timer.periodic(
      const Duration(seconds: 3),
      (timer) => _warmupNetworkConnections(),
    );
  }

  /// Optimize memory usage
  void _optimizeMemoryUsage() {
    if (kDebugMode) print('🧠 Optimizing memory usage...');
    
    // Setup memory cleanup timer
    Timer.periodic(const Duration(minutes: 2), (timer) {
      _cleanupExpiredCache();
      _optimizeDataStructures();
    });
  }

  /// Setup background optimization
  void _setupBackgroundOptimization() {
    // Continuous performance monitoring
    Timer.periodic(const Duration(seconds: 10), (timer) {
      _updatePerformanceMetrics();
    });
  }

  /// 🎯 OPTIMIZED OPERATION WRAPPER
  /// Wraps operations with performance optimization
  Future<T> optimizedExecute<T>(
    String operationName,
    Future<T> Function() operation, {
    bool useCache = true,
    Duration? cacheExpiry,
  }) async {
    final startTime = DateTime.now();
    
    try {
      // Check cache first
      if (useCache && _cachingEnabled) {
        final cachedResult = _getCachedResult<T>(operationName);
        if (cachedResult != null) {
          _recordOperationTime(operationName, 1); // Cache hit is super fast
          _updateCacheHitRate(true);
          return cachedResult;
        }
      }

      // Execute operation
      final result = await operation();
      
      // Cache result
      if (useCache && _cachingEnabled) {
        _cacheResult(operationName, result, cacheExpiry);
      }
      
      // Record performance
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      _recordOperationTime(operationName, duration);
      _updateCacheHitRate(false);
      
      return result;
    } catch (e) {
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      _recordOperationTime('${operationName}_error', duration);
      rethrow;
    }
  }

  /// Get cached result
  T? _getCachedResult<T>(String key) {
    if (!_dataCache.containsKey(key)) return null;
    
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return null;
    
    // Check if cache is expired
    if (DateTime.now().difference(timestamp) > _cacheExpiry) {
      _dataCache.remove(key);
      _cacheTimestamps.remove(key);
      return null;
    }
    
    final cached = _dataCache[key];
    return cached is T ? cached : null;
  }

  /// Cache result
  void _cacheResult<T>(String key, T result, Duration? expiry) {
    _dataCache[key] = result;
    _cacheTimestamps[key] = DateTime.now();
  }

  /// Record operation time
  void _recordOperationTime(String operation, int milliseconds) {
    _operationTimes[operation] ??= [];
    _operationTimes[operation]!.add(milliseconds);
    
    // Keep only last 50 measurements
    if (_operationTimes[operation]!.length > 50) {
      _operationTimes[operation]!.removeAt(0);
    }
  }

  /// Update cache hit rate
  void _updateCacheHitRate(bool hit) {
    final currentHits = _performanceMetrics['cache_hits'] ?? 0;
    final currentMisses = _performanceMetrics['cache_misses'] ?? 0;
    
    if (hit) {
      _performanceMetrics['cache_hits'] = currentHits + 1;
    } else {
      _performanceMetrics['cache_misses'] = currentMisses + 1;
    }
    
    final totalRequests = _performanceMetrics['cache_hits'] + _performanceMetrics['cache_misses'];
    _performanceMetrics['cache_hit_rate'] = totalRequests > 0 
        ? (_performanceMetrics['cache_hits'] / totalRequests * 100).round()
        : 0;
  }

  /// Refresh critical data in background
  void _refreshCriticalData() {
    // Update demo data to keep it fresh
    _dataCache['demo_user_profile']?['last_activity'] = DateTime.now().millisecondsSinceEpoch;
    _dataCache['ml_model_cache']?['last_prediction'] = DateTime.now().millisecondsSinceEpoch;
    _dataCache['blockchain_cache']?['last_block_time'] = DateTime.now().millisecondsSinceEpoch;
  }

  /// Warmup ML models
  void _warmupMLModels() {
    // Simulate ML model warmup
    _dataCache['ml_warmup'] = {
      'models_loaded': ['ensemble', 'behavioral', 'fraud_detection'],
      'warmup_time': DateTime.now().millisecondsSinceEpoch,
      'ready': true,
    };
  }

  /// Warmup network connections
  void _warmupNetworkConnections() {
    // Simulate network warmup
    _dataCache['network_warmup'] = {
      'connections': ['icp_mainnet', 'masumi_api', 'ml_service'],
      'latency_ms': [45, 32, 28],
      'status': 'optimal',
    };
  }

  /// Cleanup expired cache
  void _cleanupExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    _cacheTimestamps.forEach((key, timestamp) {
      if (now.difference(timestamp) > _cacheExpiry) {
        expiredKeys.add(key);
      }
    });
    
    for (final key in expiredKeys) {
      _dataCache.remove(key);
      _cacheTimestamps.remove(key);
    }
    
    if (kDebugMode && expiredKeys.isNotEmpty) {
      print('🧹 Cleaned up ${expiredKeys.length} expired cache entries');
    }
  }

  /// Optimize data structures
  void _optimizeDataStructures() {
    // Trim operation times to prevent memory bloat
    _operationTimes.forEach((operation, times) {
      if (times.length > 100) {
        _operationTimes[operation] = times.sublist(times.length - 50);
      }
    });
  }

  /// Update performance metrics
  void _updatePerformanceMetrics() {
    _performanceMetrics['total_operations'] = _operationTimes.values
        .fold(0, (sum, times) => sum + times.length);
    
    // Calculate average response time
    final allTimes = _operationTimes.values
        .expand((times) => times)
        .where((time) => time > 0)
        .toList();
    
    if (allTimes.isNotEmpty) {
      _performanceMetrics['average_response_time'] = 
          allTimes.reduce((a, b) => a + b) / allTimes.length;
    }
    
    _performanceMetrics['cache_size'] = _dataCache.length;
    _performanceMetrics['active_timers'] = _preloadTimers.length;
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    return {
      'is_optimized': _isOptimized,
      'performance_metrics': _performanceMetrics,
      'operation_stats': _getOperationStats(),
      'cache_stats': _getCacheStats(),
      'optimization_recommendations': _getOptimizationRecommendations(),
    };
  }

  /// Get operation statistics
  Map<String, dynamic> _getOperationStats() {
    final stats = <String, dynamic>{};
    
    _operationTimes.forEach((operation, times) {
      if (times.isNotEmpty) {
        stats[operation] = {
          'count': times.length,
          'average_ms': times.reduce((a, b) => a + b) / times.length,
          'min_ms': times.reduce((a, b) => a < b ? a : b),
          'max_ms': times.reduce((a, b) => a > b ? a : b),
        };
      }
    });
    
    return stats;
  }

  /// Get cache statistics
  Map<String, dynamic> _getCacheStats() {
    return {
      'total_entries': _dataCache.length,
      'hit_rate': _performanceMetrics['cache_hit_rate'] ?? 0,
      'total_hits': _performanceMetrics['cache_hits'] ?? 0,
      'total_misses': _performanceMetrics['cache_misses'] ?? 0,
      'memory_usage_estimate': _dataCache.length * 1024, // Rough estimate
    };
  }

  /// Get optimization recommendations
  List<String> _getOptimizationRecommendations() {
    final recommendations = <String>[];
    
    final hitRate = _performanceMetrics['cache_hit_rate'] ?? 0;
    if (hitRate < 70) {
      recommendations.add('Consider increasing cache expiry time');
    }
    
    final avgResponseTime = _performanceMetrics['average_response_time'] ?? 0;
    if (avgResponseTime > 500) {
      recommendations.add('Consider optimizing slow operations');
    }
    
    if (_dataCache.length > 100) {
      recommendations.add('Consider implementing cache size limits');
    }
    
    return recommendations;
  }

  /// Dispose performance optimizer
  void dispose() {
    _preloadTimers.values.forEach((timer) => timer.cancel());
    _preloadTimers.clear();
    _dataCache.clear();
    _cacheTimestamps.clear();
    _isOptimized = false;
  }
}
