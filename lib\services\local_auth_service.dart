import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'biometric_service.dart';
import 'anomaly_detector.dart';
import 'auth_service.dart'; // Add this import
import '../models/behavioral_data_model.dart';

enum LocalAuthState {
  firstTime,          // First app launch - needs onboarding
  registered,         // User registered but logged out
  authenticated,      // User authenticated and no anomalies
  requiresBiometric,  // User needs biometric verification
  requiresFullAuth,   // Anomaly detected - needs full authentication
  sessionExpired,     // Session expired - needs re-authentication
}

class LocalAuthService {
  static final LocalAuthService _instance = LocalAuthService._internal();
  factory LocalAuthService() => _instance;
  LocalAuthService._internal();

  static const String _keyIsFirstLaunch = 'is_first_launch_v2';
  static const String _keyIsRegistered = 'user_registered';
  static const String _keyLastAuthTime = 'last_auth_time';
  static const String _keyBiometricEnabled = 'biometric_enabled';
  static const String _keySessionActive = 'session_active';
  static const String _keyUserProfile = 'user_profile_encrypted';
  static const String _keyQuickAuthEnabled = 'quick_auth_enabled';
  static const String _keyAnomalyScore = 'last_anomaly_score';
  
  // Secure storage for sensitive data
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );
  
  static SharedPreferences? _prefs;
  final BiometricService _biometricService = BiometricService();
  final AnomalyDetector _anomalyDetector = AnomalyDetector();
  
  // Session management
  static const int _sessionDuration = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
  static const int _quickAuthDuration = 15 * 60 * 1000; // 15 minutes for quick auth
  
  /// Initialize the service
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Check current authentication state
  Future<LocalAuthState> checkAuthenticationState() async {
    await initialize();
    
    // Check if first launch
    final isFirstLaunch = _prefs!.getBool(_keyIsFirstLaunch) ?? true;
    if (isFirstLaunch) {
      return LocalAuthState.firstTime;
    }
    
    // Check if user is registered
    final isRegistered = _prefs!.getBool(_keyIsRegistered) ?? false;
    if (!isRegistered) {
      return LocalAuthState.firstTime;
    }
    
    // Check session validity
    final lastAuthTime = _prefs!.getInt(_keyLastAuthTime) ?? 0;
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final timeSinceAuth = currentTime - lastAuthTime;
    
    // Check if session expired
    if (timeSinceAuth > _sessionDuration) {
      await _clearSession();
      return LocalAuthState.sessionExpired;
    }
    
    // Check if within quick auth window and anomaly score is good
    if (timeSinceAuth <= _quickAuthDuration) {
      final lastAnomalyScore = _prefs!.getDouble(_keyAnomalyScore) ?? 0.0;
      
      // If no anomaly detected and within quick auth window
      if (lastAnomalyScore > 0.7) {
        // Set current user from stored profile for compatibility
        final userProfile = await _getUserProfile();
        if (userProfile != null) {
          AuthService.currentUserName = userProfile['email'];
        }
        return LocalAuthState.authenticated;
      }
    }
    
    // Check if biometric is available and enabled
    final biometricEnabled = _prefs!.getBool(_keyBiometricEnabled) ?? false;
    if (biometricEnabled && await _biometricService.isBiometricAvailable()) {
      return LocalAuthState.requiresBiometric;
    }
    
    // Default to requiring full authentication
    return LocalAuthState.requiresFullAuth;
  }

  /// Register a new user with local credentials
  Future<bool> registerUser({
    required String email,
    required String password,
    required Map<String, dynamic> userProfile,
    bool enableBiometric = true,
  }) async {
    await initialize();
    
    try {
      // Encrypt and store user credentials
      final hashedPassword = _hashPassword(password);
      final encryptedProfile = await _encryptUserProfile({
        ...userProfile,
        'email': email,
        'password_hash': hashedPassword,
        'registration_time': DateTime.now().millisecondsSinceEpoch,
      });
      
      await _secureStorage.write(key: _keyUserProfile, value: encryptedProfile);
      
      // Set registration flags
      await _prefs!.setBool(_keyIsFirstLaunch, false);
      await _prefs!.setBool(_keyIsRegistered, true);
      await _prefs!.setBool(_keyBiometricEnabled, enableBiometric);
      await _prefs!.setBool(_keyQuickAuthEnabled, true);
      
      // Set current user in AuthService for compatibility
      AuthService.currentUserName = email;
      
      // Create initial session
      await _createSession();
      
      return true;
    } catch (e) {
      print('Registration error: $e');
      return false;
    }
  }

  /// Authenticate user with email/password
  Future<bool> authenticateWithCredentials(String email, String password) async {
    await initialize();
    
    try {
      final userProfile = await _getUserProfile();
      if (userProfile == null) return false;
      
      final storedEmail = userProfile['email'];
      final storedPasswordHash = userProfile['password_hash'];
      final providedPasswordHash = _hashPassword(password);
      
      if (storedEmail == email && storedPasswordHash == providedPasswordHash) {
        // Set current user in AuthService for compatibility
        AuthService.currentUserName = email;
        
        await _createSession();
        return true;
      }
      
      return false;
    } catch (e) {
      print('Authentication error: $e');
      return false;
    }
  }

  /// Authenticate with biometrics
  Future<bool> authenticateWithBiometrics({String? reason}) async {
    await initialize();
    
    final biometricEnabled = _prefs!.getBool(_keyBiometricEnabled) ?? false;
    if (!biometricEnabled) return false;
    
    final result = await _biometricService.authenticateWithBiometrics(
      reason: reason ?? 'Authenticate to access your secure banking account',
    );
    
    if (result.success) {
      // Set current user from stored profile for compatibility
      final userProfile = await _getUserProfile();
      if (userProfile != null) {
        AuthService.currentUserName = userProfile['email'];
      }
      
      await _createSession();
      return true;
    }
    
    return false;
  }

  /// Check for anomalies in user behavior and update state
  Future<double> runAnomalyDetection(BehavioralData behavioralData) async {
    await initialize();
    
    final anomalyScore = _anomalyDetector.calculateBehavioralScore(behavioralData);
    await _prefs!.setDouble(_keyAnomalyScore, anomalyScore);
    
    print('🔍 Anomaly Detection Score: $anomalyScore');
    
    return anomalyScore;
  }

  /// Get current user profile
  Future<Map<String, dynamic>?> getUserProfile() async {
    final profile = await _getUserProfile();
    
    // Ensure current user is set in AuthService for compatibility
    if (profile != null && profile['email'] != null) {
      AuthService.currentUserName = profile['email'];
    }
    
    return profile;
  }

  /// Check if quick authentication is possible
  Future<bool> canUseQuickAuth() async {
    await initialize();
    
    final quickAuthEnabled = _prefs!.getBool(_keyQuickAuthEnabled) ?? false;
    if (!quickAuthEnabled) return false;
    
    final lastAuthTime = _prefs!.getInt(_keyLastAuthTime) ?? 0;
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final timeSinceAuth = currentTime - lastAuthTime;
    
    return timeSinceAuth <= _quickAuthDuration;
  }

  /// Enable/disable biometric authentication
  Future<void> setBiometricEnabled(bool enabled) async {
    await initialize();
    await _prefs!.setBool(_keyBiometricEnabled, enabled);
  }

  /// Check if biometric is enabled
  Future<bool> isBiometricEnabled() async {
    await initialize();
    return _prefs!.getBool(_keyBiometricEnabled) ?? false;
  }

  /// Logout user and clear session
  Future<void> logout() async {
    await initialize();
    await _clearSession();
    
    // Clear current user from AuthService for compatibility
    AuthService.currentUserName = null;
  }

  /// Clear all user data (for testing/reset)
  Future<void> resetAllData() async {
    await initialize();
    await _prefs!.clear();
    await _secureStorage.deleteAll();
    
    // Clear current user from AuthService for compatibility
    AuthService.currentUserName = null;
  }

  // Private helper methods

  Future<void> _createSession() async {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    await _prefs!.setInt(_keyLastAuthTime, currentTime);
    await _prefs!.setBool(_keySessionActive, true);
  }

  Future<void> _clearSession() async {
    await _prefs!.remove(_keyLastAuthTime);
    await _prefs!.setBool(_keySessionActive, false);
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password + 'trust_chain_salt_2025');
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<String> _encryptUserProfile(Map<String, dynamic> profile) async {
    // Simple encryption - in production, use proper encryption
    final jsonString = jsonEncode(profile);
    final bytes = utf8.encode(jsonString);
    final encoded = base64.encode(bytes);
    return encoded;
  }

  Future<Map<String, dynamic>?> _getUserProfile() async {
    try {
      final encryptedProfile = await _secureStorage.read(key: _keyUserProfile);
      if (encryptedProfile == null) return null;
      
      final decoded = base64.decode(encryptedProfile);
      final jsonString = utf8.decode(decoded);
      return jsonDecode(jsonString);
    } catch (e) {
      print('Error reading user profile: $e');
      return null;
    }
  }

  /// Get current session info
  Future<Map<String, dynamic>> getSessionInfo() async {
    await initialize();
    
    final lastAuthTime = _prefs!.getInt(_keyLastAuthTime) ?? 0;
    final sessionActive = _prefs!.getBool(_keySessionActive) ?? false;
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final timeSinceAuth = currentTime - lastAuthTime;
    
    return {
      'session_active': sessionActive,
      'last_auth_time': lastAuthTime,
      'time_since_auth_minutes': (timeSinceAuth / (1000 * 60)).round(),
      'can_quick_auth': timeSinceAuth <= _quickAuthDuration,
      'session_expires_in_minutes': ((_sessionDuration - timeSinceAuth) / (1000 * 60)).round(),
    };
  }
}
