import 'package:flutter/material.dart';
import '../screens/first_screen.dart';
import '../screens/splash_screen.dart';
import '../screens/onboarding_screen.dart';
import '../screens/register_screen.dart';
import '../screens/secure_login_screen.dart';
import '../screens/behavioral_auth_screen.dart';
import '../screens/dashboard_screen.dart';
import '../screens/activity_screen.dart';
import '../screens/payment_success_screen.dart';
import '../screens/privacy_dashboard_screen.dart';
import '../screens/behavioral_data_screen.dart';
import '../screens/session_export_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/pin_verification_screen.dart';
import '../screens/security_settings_screen.dart';
import '../screens/help_center_screen.dart';
import '../screens/icp_masumi_demo_screen.dart';
import '../models/behavioral_data_model.dart';

class AppRoutes {
  // Authentication Routes
  static const String firstScreen = '/';
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  
  // Security Routes
  static const String pinVerification = '/pin-verification';
  static const String behavioralAuth = '/behavioral-auth';
  
  // Core Banking Routes
  static const String dashboard = '/dashboard';
  static const String activity = '/activity';
  static const String profile = '/profile';
  static const String paymentSuccess = '/payment-success';
  
  // Privacy & Security
  static const String privacyDashboard = '/privacy-dashboard';
  static const String behavioralData = '/behavioral-data';
  static const String sessionExport = '/session-export';
  static const String securitySettings = '/security-settings';
  
  // Support
  static const String helpCenter = '/help-center';
  
  // 🚀 HACKATHON: ICP and Masumi Demo
  static const String icpMasumiDemo = '/icp-masumi-demo';
  
  // Error
  static const String error = '/error';
}

class AppRouter {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.firstScreen:
        return MaterialPageRoute(builder: (_) => const FirstScreen());

      case AppRoutes.splash:
        return MaterialPageRoute(builder: (_) => const SplashScreen());

      case AppRoutes.onboarding:
        return MaterialPageRoute(builder: (_) => const OnboardingScreen());

      case AppRoutes.login:
        return MaterialPageRoute(builder: (_) => const SecureLoginScreen());

      case AppRoutes.register:
        return MaterialPageRoute(builder: (_) => const RegisterScreen());

      case AppRoutes.behavioralAuth:
        return MaterialPageRoute(builder: (_) => const BehavioralAuthScreen());

      case AppRoutes.dashboard:
        return MaterialPageRoute(builder: (_) => const DashboardScreen());

      case AppRoutes.activity:
        return MaterialPageRoute(builder: (_) => const ActivityScreen());
      
      case AppRoutes.paymentSuccess:
        return MaterialPageRoute(builder: (_) => const PaymentSuccessScreen());
      
      case AppRoutes.privacyDashboard:
        return MaterialPageRoute(builder: (_) => const PrivacyDashboardScreen());
      
      case AppRoutes.behavioralData:
        final args = settings.arguments as Map<String, dynamic>?;
        final data = args?['data'] as BehavioralData?;
        return MaterialPageRoute(
          builder: (_) => BehavioralDataScreen(data: data),
        );

      case AppRoutes.sessionExport:
        final args = settings.arguments as Map<String, dynamic>?;
        final userId = args?['userId'] as String? ?? 'default_user';
        final currentSession = args?['currentSession'] as BehavioralData?;
        return MaterialPageRoute(
          builder: (_) => SessionExportScreen(
            userId: userId,
            currentSession: currentSession,
          ),
        );

      case AppRoutes.profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());

      case AppRoutes.helpCenter:
        return MaterialPageRoute(builder: (_) => const HelpCenterScreen());

      case AppRoutes.icpMasumiDemo:
        return MaterialPageRoute(builder: (_) => const ICPMasumiDemoScreen());

      case AppRoutes.securitySettings:
        return MaterialPageRoute(builder: (_) => const SecuritySettingsScreen());

      case AppRoutes.pinVerification:
        return MaterialPageRoute(builder: (_) => const PinVerificationScreen());

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('Page Not Found')),
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red),
                  SizedBox(height: 16),
                  Text(
                    'Page Not Found',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('The requested page could not be found.'),
                ],
              ),
            ),
          ),
        );
    }
  }
}

// Error handling for unknown routes
class ErrorScreen extends StatelessWidget {
  final String title;
  final String message;
  
  const ErrorScreen({
    Key? key,
    required this.title,
    required this.message,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(message),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.pushReplacementNamed(context, AppRoutes.dashboard),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    );
  }
}
