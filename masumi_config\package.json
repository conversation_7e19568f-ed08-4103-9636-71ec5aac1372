{"name": "trust-chain-masumi-config", "version": "1.0.0", "description": "Masumi Differential Privacy Configuration for Trust Chain Banking", "main": "privacy_config.js", "type": "module", "dependencies": {"@masumi/privacy-core": "^2.1.0", "@masumi/mechanisms": "^1.8.0", "@masumi/budget-tracking": "^1.5.0", "differential-privacy": "^3.2.0", "crypto-browserify": "^3.12.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0"}, "scripts": {"build": "tsc", "test": "node test/privacy_test.js", "install-deps": "npm install"}, "engines": {"node": ">=16.0.0"}}