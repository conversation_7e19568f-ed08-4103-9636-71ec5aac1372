import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../services/auth_service.dart';
import '../services/local_auth_service.dart';
import '../services/background_anomaly_service.dart';
import '../routes/app_routes.dart';
import '../utils/constants.dart' as constants;
import '../services/secure_storage_service.dart';
import 'package:geolocator/geolocator.dart';
import '../services/location_permission_service.dart';
import '../services/geo_fence_service.dart';
import '../utils/constants.dart'; // assuming it contains your safe location values


class FirstScreen extends StatefulWidget {
  const FirstScreen({Key? key}) : super(key: key);

  @override
  State<FirstScreen> createState() => _FirstScreenState();
}

class _FirstScreenState extends State<FirstScreen>
    with TickerProviderStateMixin {
  late AnimationController _glowController;
  late AnimationController _logoController;
  late AnimationController _textController;
  late Animation<double> _glowAnimation;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _textFadeAnimation;
  late Animation<Offset> _textSlideAnimation;

  bool _isInitializing = true;
  String _statusText = 'Initializing secure banking...';
  
  final LocalAuthService _localAuth = LocalAuthService();
  final BackgroundAnomalyService _anomalyService = BackgroundAnomalyService();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startInitialization();
  }

  void _setupAnimations() {
    // Glow animation for the logo
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    // Logo scale animation
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    // Text animations
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    _logoScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _logoController.forward();
    _glowController.repeat(reverse: true);
    
    Future.delayed(const Duration(milliseconds: 800), () {
      _textController.forward();
    });
  }

  Future<void> _startInitialization() async {
    try {
      // Show the first screen for a minimum duration
      await Future.delayed(const Duration(milliseconds: 2000));
      
      // Start background anomaly detection
      setState(() => _statusText = 'Starting security monitoring...');
      await _anomalyService.startBackgroundDetection();
      await Future.delayed(const Duration(milliseconds: 800));
      
      // Check authentication state with new local auth service
      setState(() => _statusText = 'Checking authentication state...');
      final authState = await _localAuth.checkAuthenticationState();
      await Future.delayed(const Duration(milliseconds: 600));
      
      // Navigate based on auth state
      _navigateBasedOnLocalAuthState(authState);
      
    } catch (e) {
      setState(() {
        _statusText = 'Initialization failed';
        _isInitializing = false;
      });
    }
  }

  void _navigateBasedOnLocalAuthState(LocalAuthState authState) async {
    switch (authState) {
      case LocalAuthState.firstTime:
        Navigator.pushReplacementNamed(context, AppRoutes.onboarding);
        break;
        
      case LocalAuthState.registered:
        Navigator.pushReplacementNamed(context, AppRoutes.login);
        break;
        
      case LocalAuthState.authenticated:
        // User is authenticated and no anomalies - go directly to dashboard
        setState(() => _statusText = 'Welcome back! Accessing dashboard...');
        
        // Ensure current user is loaded
        await _localAuth.getUserProfile();
        
        await Future.delayed(const Duration(milliseconds: 1000));
        Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
        break;
        
      case LocalAuthState.requiresBiometric:
        // Try biometric authentication first
        setState(() => _statusText = 'Authenticating with biometrics...');
        final biometricSuccess = await _localAuth.authenticateWithBiometrics(
          reason: 'Access your secure banking account',
        );

        if (biometricSuccess) {
          // Simplified flow - check for basic anomalies only
          final anomalyScore = await _anomalyService.forceAnomalyCheck();

          if (anomalyScore >= 0.6) {
            setState(() => _statusText = 'Authentication successful! Loading dashboard...');

            // Load user profile and proceed to dashboard
            await _localAuth.getUserProfile();
            await Future.delayed(const Duration(milliseconds: 800));
            Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
          } else {
            // Anomaly detected - require full authentication
            setState(() => _statusText = 'Security check required...');
            await Future.delayed(const Duration(milliseconds: 1000));
            Navigator.pushReplacementNamed(context, AppRoutes.behavioralAuth);
          }
        } else {
          // Biometric failed - fallback to login screen
          Navigator.pushReplacementNamed(context, AppRoutes.login);
        }
        break;
        
      case LocalAuthState.requiresFullAuth:
        // Anomaly detected or session requires full verification
        Navigator.pushReplacementNamed(context, AppRoutes.behavioralAuth);
        break;
        
      case LocalAuthState.sessionExpired:
        // Session expired - back to login
        Navigator.pushReplacementNamed(context, AppRoutes.login);
        break;
    }
  }

  @override
  void dispose() {
    _glowController.dispose();
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: constants.AppColors.bankingBackground,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              constants.AppColors.bankingBackground,
              constants.AppColors.bankingSurface,
              constants.AppColors.bankingBackground,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                flex: 3,
                child: Center(
                  child: AnimatedBuilder(
                    animation: Listenable.merge([_logoScaleAnimation, _glowAnimation]),
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _logoScaleAnimation.value,
                        child: Container(
                          width: 200,
                          height: 200,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: constants.AppColors.bankingPrimary.withOpacity(_glowAnimation.value * 0.6),
                                blurRadius: 40 * _glowAnimation.value,
                                spreadRadius: 10 * _glowAnimation.value,
                              ),
                              BoxShadow(
                                color: constants.AppColors.bankingSecondary.withOpacity(_glowAnimation.value * 0.4),
                                blurRadius: 60 * _glowAnimation.value,
                                spreadRadius: 15 * _glowAnimation.value,
                              ),
                            ],
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                colors: [
                                  constants.AppColors.bankingPrimary,
                                  constants.AppColors.bankingSecondary,
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                // Hand icon
                                Icon(
                                  Icons.pan_tool_rounded,
                                  size: 60,
                                  color: Colors.white.withOpacity(0.9),
                                ),
                                // Money/Currency overlay
                                Positioned(
                                  top: 45,
                                  right: 45,
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: constants.AppColors.bankingAccent,
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: constants.AppColors.bankingAccent.withOpacity(0.5),
                                          blurRadius: 10,
                                          spreadRadius: 2,
                                        ),
                                      ],
                                    ),
                                    child: Text(
                                      constants.BankingConstants.currency,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              Expanded(
                flex: 2,
                child: SlideTransition(
                  position: _textSlideAnimation,
                  child: FadeTransition(
                    opacity: _textFadeAnimation,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // App Title
                        Shimmer.fromColors(
                          baseColor: Colors.white,
                          highlightColor: constants.AppColors.bankingPrimary,
                          child: Text(
                            constants.BankingConstants.appName,
                            style: const TextStyle(
                              fontSize: 42,
                              fontWeight: FontWeight.w800,
                              letterSpacing: 2.0,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // Tagline
                        Text(
                          constants.BankingConstants.tagline,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            letterSpacing: 0.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 40),
                        
                        // Status text
                        if (_isInitializing) ...[
                          SizedBox(
                            width: 250,
                            child: LinearProgressIndicator(
                              backgroundColor: constants.AppColors.bankingSurface,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                constants.AppColors.bankingPrimary,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _statusText,
                            style: const TextStyle(
                              color: Colors.white60,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
              
              // Footer
              Padding(
                padding: const EdgeInsets.all(24),
                child: Text(
                  'Secured by advanced behavioral authentication',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.4),
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
