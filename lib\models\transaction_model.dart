import 'package:flutter/material.dart';

enum TransactionType { credit, debit }
enum TransactionCategory { 
  salary, 
  transfer, 
  payment, 
  shopping, 
  investment, 
  interest, 
  withdrawal, 
  deposit,
  utility,
  food,
  transport,
  entertainment,
  other 
}

class Transaction {
  final String id;
  final String title;
  final String description;
  final double amount;
  final TransactionType type;
  final TransactionCategory category;
  final DateTime timestamp;
  final IconData icon;
  final String? recipientName;
  final String? recipientAccount;
  final String? referenceNumber;
  final Map<String, dynamic>? metadata;

  Transaction({
    required this.id,
    required this.title,
    required this.description,
    required this.amount,
    required this.type,
    required this.category,
    required this.timestamp,
    required this.icon,
    this.recipientName,
    this.recipientAccount,
    this.referenceNumber,
    this.metadata,
  });

  bool get isCredit => type == TransactionType.credit;
  bool get isDebit => type == TransactionType.debit;

  String get formattedAmount {
    final sign = isCredit ? '+' : '-';
    return '$sign₹${amount.toStringAsFixed(2)}';
  }

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'amount': amount,
      'type': type.toString(),
      'category': category.toString(),
      'timestamp': timestamp.toIso8601String(),
      'recipientName': recipientName,
      'recipientAccount': recipientAccount,
      'referenceNumber': referenceNumber,
      'metadata': metadata,
    };
  }

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      amount: json['amount'].toDouble(),
      type: TransactionType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
      category: TransactionCategory.values.firstWhere(
        (e) => e.toString() == json['category'],
      ),
      timestamp: DateTime.parse(json['timestamp']),
      icon: getIconForCategory(TransactionCategory.values.firstWhere(
        (e) => e.toString() == json['category'],
      )),
      recipientName: json['recipientName'],
      recipientAccount: json['recipientAccount'],
      referenceNumber: json['referenceNumber'],
      metadata: json['metadata'],
    );
  }

  static IconData getIconForCategory(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.salary:
        return Icons.account_balance;
      case TransactionCategory.transfer:
        return Icons.person;
      case TransactionCategory.payment:
        return Icons.payment;
      case TransactionCategory.shopping:
        return Icons.shopping_cart;
      case TransactionCategory.investment:
        return Icons.trending_up;
      case TransactionCategory.interest:
        return Icons.trending_up;
      case TransactionCategory.withdrawal:
        return Icons.atm;
      case TransactionCategory.deposit:
        return Icons.account_balance;
      case TransactionCategory.utility:
        return Icons.electrical_services;
      case TransactionCategory.food:
        return Icons.restaurant;
      case TransactionCategory.transport:
        return Icons.directions_car;
      case TransactionCategory.entertainment:
        return Icons.movie;
      case TransactionCategory.other:
        return Icons.more_horiz;
    }
  }
}

class TransactionSummary {
  final double totalIncome;
  final double totalExpense;
  final double netAmount;
  final int transactionCount;
  final DateTime periodStart;
  final DateTime periodEnd;

  TransactionSummary({
    required this.totalIncome,
    required this.totalExpense,
    required this.netAmount,
    required this.transactionCount,
    required this.periodStart,
    required this.periodEnd,
  });

  String get formattedIncome => '₹${totalIncome.toStringAsFixed(2)}';
  String get formattedExpense => '₹${totalExpense.toStringAsFixed(2)}';
  String get formattedNet => '₹${netAmount.toStringAsFixed(2)}';
}
