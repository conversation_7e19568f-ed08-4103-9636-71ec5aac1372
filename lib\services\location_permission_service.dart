import 'package:geolocator/geolocator.dart';

class LocationPermissionService {
  /// Checks if location services are enabled (GPS ON).
  static Future<bool> isGpsEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }
 static Future<bool> ensureLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever) {
      permission = await Geolocator.requestPermission();
    }
    return permission == LocationPermission.always || permission == LocationPermission.whileInUse;
  }
  /// Requests location permission from the user.
  static Future<bool> requestLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.deniedForever) {
      return false; // User has permanently denied permission.
    }

    return permission == LocationPermission.always || permission == LocationPermission.whileInUse;
  }

  /// Requests both: location permission + ensures GPS is enabled.
  static Future<bool> ensureLocationReady() async {
    final isEnabled = await isGpsEnabled();
    if (!isEnabled) return false;

    final hasPermission = await requestLocationPermission();
    return hasPermission;
  }
}
