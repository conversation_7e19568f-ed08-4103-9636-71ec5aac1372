import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';

class GeoFenceService {
  static const double _maxDistanceMeters = 5000;

  static Future<void> saveCurrentLocation(Position position) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('allowed_lat', position.latitude);
    await prefs.setDouble('allowed_lon', position.longitude);
  }

  static Future<bool> isWithinAllowedRadius(Position currentPosition) async {
    final prefs = await SharedPreferences.getInstance();
    final allowedLat = prefs.getDouble('allowed_lat');
    final allowedLon = prefs.getDouble('allowed_lon');

    if (allowedLat == null || allowedLon == null) return false;

    final distance = Geolocator.distanceBetween(
      currentPosition.latitude,
      currentPosition.longitude,
      allowedLat,
      allowedLon,
    );

    return distance <= _maxDistanceMeters;
  }
  
  static Future<bool> hasSavedLocation() async {
  final prefs = await SharedPreferences.getInstance();
  final allowedLat = prefs.getDouble('allowed_lat');
  final allowedLon = prefs.getDouble('allowed_lon');
  return allowedLat != null && allowedLon != null;
}

}
